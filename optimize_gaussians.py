#!/usr/bin/env python3
"""
Gaussian优化脚本 - 过滤和优化Gaussian以提高渲染质量
"""

import os
import torch
import numpy as np
import yaml
from typing import Dict, Tuple
from dataclasses import dataclass
import tyro

from keyframes_dataset import KeyframesDataset


@dataclass
class GaussianOptimizeConfig:
    """Gaussian优化配置"""
    
    # Input/Output
    ckpt_path: str  # 输入checkpoint路径
    output_path: str = ""  # 输出路径，默认为输入路径_optimized
    
    # 过滤参数
    opacity_threshold: float = 0.005  # 透明度阈值
    scale_threshold: float = 0.1  # 尺度阈值（过滤过大的Gaussian）
    max_gaussians: int = 2000000  # 最大保留的Gaussian数量
    
    # 优化策略
    use_opacity_filter: bool = True  # 使用透明度过滤
    use_scale_filter: bool = True  # 使用尺度过滤
    use_position_filter: bool = True  # 使用位置过滤（移除离群点）
    use_color_filter: bool = False  # 使用颜色过滤
    
    # 位置过滤参数
    position_std_threshold: float = 3.0  # 位置标准差阈值
    
    # 设备
    device: str = "cuda"


class GaussianOptimizer:
    """Gaussian优化器"""
    
    def __init__(self, config: GaussianOptimizeConfig):
        self.config = config
        self.device = config.device
        
        # 设置输出路径
        if not config.output_path:
            base_path = config.ckpt_path.replace('.pt', '_optimized.pt')
            self.config.output_path = base_path
        
        print(f"Gaussian优化器初始化")
        print(f"输入: {config.ckpt_path}")
        print(f"输出: {self.config.output_path}")
    
    def load_checkpoint(self) -> Tuple[Dict, Dict]:
        """加载checkpoint和配置"""
        print("加载checkpoint...")
        ckpt = torch.load(self.config.ckpt_path, map_location=self.device, weights_only=False)
        
        # 加载训练配置
        cfg_path = os.path.join(os.path.dirname(self.config.ckpt_path), "..", "cfg.yml")
        training_cfg = {}
        if os.path.exists(cfg_path):
            try:
                with open(cfg_path, 'r') as f:
                    # 读取文件内容并过滤掉Python对象
                    content = f.read()
                    lines = content.split('\n')
                    filtered_lines = []
                    skip_next = False

                    for line in lines:
                        if 'python/object:' in line:
                            skip_next = True
                            continue
                        if skip_next and line.startswith('  '):
                            continue
                        skip_next = False
                        filtered_lines.append(line)

                    filtered_content = '\n'.join(filtered_lines)
                    training_cfg = yaml.safe_load(filtered_content)
                print(f"✅ 加载训练配置: {cfg_path}")
            except Exception as e:
                print(f"⚠️  加载配置文件失败: {e}，使用默认参数")
                training_cfg = {}
        
        splats = ckpt["splats"]
        num_gaussians = splats["means"].shape[0]
        print(f"✅ 加载完成: {num_gaussians:,} Gaussians")
        
        return ckpt, training_cfg
    
    def analyze_gaussians(self, splats: Dict) -> Dict:
        """分析Gaussian分布"""
        print("\n=== Gaussian分析 ===")
        
        means = splats["means"]
        scales = torch.exp(splats["scales"])
        opacities = torch.sigmoid(splats["opacities"])
        
        stats = {
            'total_gaussians': len(means),
            'opacity_stats': {
                'mean': opacities.mean().item(),
                'std': opacities.std().item(),
                'min': opacities.min().item(),
                'max': opacities.max().item(),
                'median': opacities.median().item(),
            },
            'scale_stats': {
                'mean': scales.mean().item(),
                'std': scales.std().item(),
                'min': scales.min().item(),
                'max': scales.max().item(),
                'median': scales.median().item(),
            },
            'position_stats': {
                'mean': means.mean(dim=0).cpu().numpy(),
                'std': means.std(dim=0).cpu().numpy(),
                'min': means.min(dim=0)[0].cpu().numpy(),
                'max': means.max(dim=0)[0].cpu().numpy(),
            }
        }
        
        print(f"总Gaussians: {stats['total_gaussians']:,}")
        print(f"透明度: 均值={stats['opacity_stats']['mean']:.4f}, "
              f"中位数={stats['opacity_stats']['median']:.4f}, "
              f"范围=[{stats['opacity_stats']['min']:.4f}, {stats['opacity_stats']['max']:.4f}]")
        print(f"尺度: 均值={stats['scale_stats']['mean']:.4f}, "
              f"中位数={stats['scale_stats']['median']:.4f}, "
              f"范围=[{stats['scale_stats']['min']:.4f}, {stats['scale_stats']['max']:.4f}]")
        
        return stats
    
    def filter_by_opacity(self, splats: Dict) -> Tuple[Dict, torch.Tensor]:
        """基于透明度过滤"""
        if not self.config.use_opacity_filter:
            return splats, torch.ones(len(splats["means"]), dtype=torch.bool, device=self.device)
        
        opacities = torch.sigmoid(splats["opacities"])
        mask = opacities > self.config.opacity_threshold
        
        print(f"透明度过滤: {mask.sum().item():,}/{len(mask):,} "
              f"({100*mask.sum().item()/len(mask):.1f}%) 保留")
        
        return self.apply_mask(splats, mask), mask
    
    def filter_by_scale(self, splats: Dict, mask: torch.Tensor) -> Tuple[Dict, torch.Tensor]:
        """基于尺度过滤"""
        if not self.config.use_scale_filter:
            return splats, mask

        scales = torch.exp(splats["scales"])
        max_scale = scales.max(dim=1)[0]  # 每个Gaussian的最大尺度
        scale_mask = max_scale < self.config.scale_threshold

        # 确保mask尺寸匹配
        if len(scale_mask) != len(mask):
            # 如果splats已经被过滤过，需要创建一个新的mask
            original_mask = torch.ones(len(scale_mask), dtype=torch.bool, device=self.device)
            original_mask = original_mask & scale_mask
            combined_mask = original_mask
        else:
            combined_mask = mask & scale_mask

        print(f"尺度过滤: {combined_mask.sum().item():,}/{len(scale_mask):,} "
              f"({100*combined_mask.sum().item()/len(scale_mask):.1f}%) 保留")

        return self.apply_mask(splats, combined_mask), combined_mask
    
    def filter_by_position(self, splats: Dict, mask: torch.Tensor) -> Tuple[Dict, torch.Tensor]:
        """基于位置过滤（移除离群点）"""
        if not self.config.use_position_filter:
            return splats, mask
        
        means = splats["means"]
        
        # 计算位置的统计信息
        mean_pos = means.mean(dim=0)
        std_pos = means.std(dim=0)
        
        # 过滤离群点
        distances = torch.norm((means - mean_pos) / std_pos, dim=1)
        position_mask = distances < self.config.position_std_threshold
        
        combined_mask = mask & position_mask
        
        print(f"位置过滤: {combined_mask.sum().item():,}/{len(mask):,} "
              f"({100*combined_mask.sum().item()/len(mask):.1f}%) 保留")
        
        return self.apply_mask(splats, combined_mask), combined_mask
    
    def filter_by_importance(self, splats: Dict, mask: torch.Tensor) -> Tuple[Dict, torch.Tensor]:
        """基于重要性过滤（保留最重要的Gaussian）"""
        if mask.sum() <= self.config.max_gaussians:
            return splats, mask
        
        # 计算重要性分数（透明度 * 尺度的倒数）
        opacities = torch.sigmoid(splats["opacities"])
        scales = torch.exp(splats["scales"])
        avg_scale = scales.mean(dim=1)
        
        # 重要性 = 透明度 / 平均尺度（小尺度更重要）
        importance = opacities / (avg_scale + 1e-8)
        
        # 只考虑当前mask中的Gaussian
        masked_importance = torch.full_like(importance, -1.0)
        masked_importance[mask] = importance[mask]
        
        # 选择最重要的
        _, indices = torch.topk(masked_importance, self.config.max_gaussians)
        final_mask = torch.zeros_like(mask)
        final_mask[indices] = True
        
        print(f"重要性过滤: {final_mask.sum().item():,}/{len(mask):,} "
              f"({100*final_mask.sum().item()/len(mask):.1f}%) 保留")
        
        return self.apply_mask(splats, final_mask), final_mask
    
    def apply_mask(self, splats: Dict, mask: torch.Tensor) -> Dict:
        """应用mask到splats"""
        filtered_splats = {}
        for key, value in splats.items():
            if isinstance(value, torch.Tensor) and len(value) == len(mask):
                filtered_splats[key] = value[mask]
            else:
                filtered_splats[key] = value
        return filtered_splats
    
    def optimize(self) -> str:
        """执行优化"""
        print("=== 开始Gaussian优化 ===")
        
        # 加载数据
        ckpt, training_cfg = self.load_checkpoint()
        splats = ckpt["splats"]
        
        # 移动到GPU
        for key in splats:
            if isinstance(splats[key], torch.Tensor):
                splats[key] = splats[key].to(self.device)
        
        # 分析原始数据
        original_stats = self.analyze_gaussians(splats)
        
        # 逐步过滤 - 重新设计为基于原始数据的过滤
        print(f"\n=== 开始过滤 ===")

        # 创建初始mask（所有Gaussian都有效）
        total_gaussians = len(splats["means"])
        final_mask = torch.ones(total_gaussians, dtype=torch.bool, device=self.device)

        # 1. 透明度过滤
        if self.config.use_opacity_filter:
            opacities = torch.sigmoid(splats["opacities"])
            opacity_mask = opacities > self.config.opacity_threshold
            final_mask = final_mask & opacity_mask
            print(f"透明度过滤: {final_mask.sum().item():,}/{total_gaussians:,} "
                  f"({100*final_mask.sum().item()/total_gaussians:.1f}%) 保留")

        # 2. 尺度过滤
        if self.config.use_scale_filter:
            scales = torch.exp(splats["scales"])
            max_scale = scales.max(dim=1)[0]
            scale_mask = max_scale < self.config.scale_threshold
            final_mask = final_mask & scale_mask
            print(f"尺度过滤: {final_mask.sum().item():,}/{total_gaussians:,} "
                  f"({100*final_mask.sum().item()/total_gaussians:.1f}%) 保留")

        # 3. 位置过滤
        if self.config.use_position_filter:
            means = splats["means"]
            mean_pos = means.mean(dim=0)
            std_pos = means.std(dim=0)
            distances = torch.norm((means - mean_pos) / std_pos, dim=1)
            position_mask = distances < self.config.position_std_threshold
            final_mask = final_mask & position_mask
            print(f"位置过滤: {final_mask.sum().item():,}/{total_gaussians:,} "
                  f"({100*final_mask.sum().item()/total_gaussians:.1f}%) 保留")

        # 4. 重要性过滤（如果还是太多）
        if final_mask.sum() > self.config.max_gaussians:
            opacities = torch.sigmoid(splats["opacities"])
            scales = torch.exp(splats["scales"])
            avg_scale = scales.mean(dim=1)
            importance = opacities / (avg_scale + 1e-8)

            # 只考虑当前mask中的Gaussian
            masked_importance = torch.full_like(importance, -1.0)
            masked_importance[final_mask] = importance[final_mask]

            # 选择最重要的
            _, indices = torch.topk(masked_importance, self.config.max_gaussians)
            final_mask = torch.zeros_like(final_mask)
            final_mask[indices] = True
            print(f"重要性过滤: {final_mask.sum().item():,}/{total_gaussians:,} "
                  f"({100*final_mask.sum().item()/total_gaussians:.1f}%) 保留")

        # 应用最终mask
        splats = self.apply_mask(splats, final_mask)
        
        # 分析优化后的数据
        print(f"\n=== 优化结果 ===")
        optimized_stats = self.analyze_gaussians(splats)
        
        reduction_ratio = optimized_stats['total_gaussians'] / original_stats['total_gaussians']
        print(f"压缩比: {reduction_ratio:.3f} "
              f"({original_stats['total_gaussians']:,} -> {optimized_stats['total_gaussians']:,})")
        
        # 保存优化后的checkpoint
        optimized_ckpt = ckpt.copy()
        optimized_ckpt["splats"] = splats
        
        # 移动回CPU保存
        for key in splats:
            if isinstance(splats[key], torch.Tensor):
                splats[key] = splats[key].cpu()
        
        torch.save(optimized_ckpt, self.config.output_path)
        print(f"✅ 优化完成，保存至: {self.config.output_path}")
        
        return self.config.output_path


def main():
    """主函数"""
    config = tyro.cli(GaussianOptimizeConfig)
    
    print("=== Gaussian优化工具 ===")
    print(f"配置: {config}")
    
    optimizer = GaussianOptimizer(config)
    output_path = optimizer.optimize()
    
    print(f"\n✅ 优化完成!")
    print(f"优化后的模型: {output_path}")
    print(f"可以使用以下命令测试渲染效果:")
    print(f"python keyframes_renderer_fixed.py --ckpt_path {output_path}")


if __name__ == "__main__":
    main()
