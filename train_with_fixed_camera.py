#!/usr/bin/env python3
"""
使用修复相机位姿的训练脚本
"""

import torch
import numpy as np
from keyframes_trainer import KeyframesTrainer, KeyframesConfig
from keyframes_dataset import KeyframesDataset
from gsplat.strategy import DefaultStrategy


class FixedCameraDataset(KeyframesDataset):
    """使用修复相机位姿的数据集"""
    
    def __init__(self, *args, camera_fix_method="backward", **kwargs):
        self.camera_fix_method = camera_fix_method
        super().__init__(*args, **kwargs)
    
    def _load_data(self):
        """重写数据加载，修复相机位姿"""
        # 先调用父类方法
        super()._load_data()
        
        # 修复每个数据项的相机位姿
        for data_item in self.data_list:
            original_camtoworld = data_item['camtoworld']
            
            if self.camera_fix_method == "flip_z":
                # 方法1: 翻转Z轴
                fixed_camtoworld = original_camtoworld.copy()
                fixed_camtoworld[2, 3] *= -1  # 翻转Z位置
                fixed_camtoworld[:3, 2] *= -1  # 翻转Z轴方向
                
            elif self.camera_fix_method == "backward":
                # 方法2: 相机后退（推荐）
                fixed_camtoworld = original_camtoworld.copy()
                # 计算点云中心
                scene_center = np.array([0.0, 0.0, 0.0])  # 已归一化的点云中心
                # 将相机放在点云前方
                camera_distance = 3.0
                fixed_camtoworld[:3, 3] = scene_center + np.array([0, 0, camera_distance])
                
            else:
                # 保持原始位姿
                fixed_camtoworld = original_camtoworld
            
            data_item['camtoworld'] = fixed_camtoworld
            print(f"修复相机位姿: {self.camera_fix_method}")
            print(f"原始位置: {original_camtoworld[:3, 3]}")
            print(f"修复位置: {fixed_camtoworld[:3, 3]}")


def main():
    """使用修复相机位姿的训练"""
    
    # 保守的策略
    strategy = DefaultStrategy(
        prune_opa=0.005,        # 适中的修剪阈值
        grow_grad2d=0.0002,     
        grow_scale3d=0.01,      
        grow_scale2d=0.05,      
        prune_scale3d=0.1,      
        prune_scale2d=0.15,     
        refine_start_iter=500,  # 延迟开始修剪
        refine_stop_iter=7000,  
        reset_every=3000,       
        refine_every=100,       
        verbose=True
    )
    
    cfg = KeyframesConfig(
        # 数据设置
        data_path="data/keyframes/24.pkl",
        result_dir="results/keyframes_fixed_camera",
        
        # 训练设置
        max_steps=10000,
        batch_size=1,
        eval_steps=[2000, 5000, 10000],
        save_steps=[2000, 5000, 10000],
        
        # 模型设置
        init_opacity=0.5,      
        init_scale=5.0,        # 适中的初始尺度
        
        # 学习率设置
        means_lr=1.6e-4,       
        scales_lr=5e-3,        
        opacities_lr=5e-2,     
        quats_lr=1e-3,         
        sh0_lr=2.5e-3,         
        shN_lr=1.25e-4,        
        
        # 损失函数设置
        ssim_lambda=0.2,       
        
        # 数据处理
        confidence_threshold=0.0,  
        max_points=None,           
        normalize_points=True,
        
        # 正则化
        opacity_reg=0.0,
        scale_reg=0.0,
        
        # 策略
        strategy=strategy,
        
        # 日志设置
        tb_every=100,
        tb_save_image=False,
        
        # 设备
        device="cuda"
    )
    
    print("🔧 开始使用修复相机位姿的训练...")
    print("修复方法: 相机后退 (最佳效果)")
    
    # 创建自定义训练器
    class FixedCameraTrainer(KeyframesTrainer):
        def __init__(self, cfg):
            # 重写数据集创建
            self.cfg = cfg
            self.device = cfg.device
            
            # 设置输出目录
            import os
            os.makedirs(cfg.result_dir, exist_ok=True)
            self.ckpt_dir = f"{cfg.result_dir}/ckpts"
            os.makedirs(self.ckpt_dir, exist_ok=True)
            self.stats_dir = f"{cfg.result_dir}/stats"
            os.makedirs(self.stats_dir, exist_ok=True)
            self.render_dir = f"{cfg.result_dir}/renders"
            os.makedirs(self.render_dir, exist_ok=True)
            
            # Tensorboard
            from torch.utils.tensorboard import SummaryWriter
            self.writer = SummaryWriter(log_dir=f"{cfg.result_dir}/tb")
            
            # 使用修复的数据集
            print(f"Loading dataset from: {cfg.data_path}")
            self.dataset = FixedCameraDataset(
                data_path=cfg.data_path,
                confidence_threshold=cfg.confidence_threshold,
                max_points=cfg.max_points,
                normalize_points=cfg.normalize_points,
                device=self.device,
                camera_fix_method="backward"  # 使用相机后退方法
            )
            
            # 创建parser
            from keyframes_dataset import KeyframesParser
            self.parser = KeyframesParser(self.dataset)
            self.scene_scale = self.parser.scene_scale
            
            print(f"Dataset loaded: {len(self.dataset)} frames")
            print(f"Scene scale: {self.scene_scale:.3f}")
            
            # 初始化模型
            from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
            self.splats, self.optimizers = create_splats_with_optimizers_keyframes(
                self.parser,
                init_opacity=cfg.init_opacity,
                init_scale=cfg.init_scale,
                means_lr=cfg.means_lr,
                scales_lr=cfg.scales_lr,
                opacities_lr=cfg.opacities_lr,
                quats_lr=cfg.quats_lr,
                sh0_lr=cfg.sh0_lr,
                shN_lr=cfg.shN_lr,
                scene_scale=self.scene_scale,
                sh_degree=cfg.sh_degree,
                device=self.device,
            )
            
            print(f"Model initialized. Number of GS: {len(self.splats['means'])}")
            
            # 初始化策略
            self.cfg.strategy.check_sanity(self.splats, self.optimizers)
            if hasattr(self.cfg.strategy, 'initialize_state'):
                try:
                    self.strategy_state = self.cfg.strategy.initialize_state(scene_scale=self.scene_scale)
                except:
                    self.strategy_state = self.cfg.strategy.initialize_state()
            
            # 初始化指标
            from torchmetrics.image import PeakSignalNoiseRatio, StructuralSimilarityIndexMeasure
            from torchmetrics.image.lpip import LearnedPerceptualImagePatchSimilarity
            self.ssim = StructuralSimilarityIndexMeasure(data_range=1.0).to(self.device)
            self.psnr = PeakSignalNoiseRatio(data_range=1.0).to(self.device)
            self.lpips = LearnedPerceptualImagePatchSimilarity(
                net_type="alex", normalize=True
            ).to(self.device)
    
    trainer = FixedCameraTrainer(cfg)
    trainer.train()
    
    print("🎉 修复相机位姿训练完成!")
    
    # 验证训练效果
    print("\n🔍 验证训练效果...")
    try:
        ckpt = torch.load(f"{cfg.result_dir}/ckpts/ckpt_9999.pt", map_location="cuda", weights_only=False)
        splats = ckpt['splats']
        
        opacities_sigmoid = torch.sigmoid(splats["opacities"])
        scales_exp = torch.exp(splats["scales"])
        
        print(f"✅ 最终透明度范围: [{opacities_sigmoid.min():.3f}, {opacities_sigmoid.max():.3f}]")
        print(f"✅ 最终尺度范围: [{scales_exp.min():.3f}, {scales_exp.max():.3f}]")
        print(f"✅ 剩余Gaussian数量: {len(splats['means'])}")
        
        # 测试渲染
        print("\n🎨 测试最终渲染...")
        from gsplat.rendering import rasterization
        
        # 使用修复的相机位姿
        data_item = trainer.dataset[0]
        camtoworld = torch.from_numpy(data_item['camtoworld']).float().cuda()
        K = torch.from_numpy(data_item['K']).float().cuda()
        
        means = splats["means"]
        quats = splats["quats"]
        scales = scales_exp
        opacities = opacities_sigmoid
        colors = torch.cat([splats["sh0"], splats["shN"]], 1)
        
        render_colors, _, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=512,
            height=288,
            sh_degree=3,
        )
        
        visible_gaussians = (info['radii'] > 0).sum().item()
        max_color = render_colors.max().item()
        
        print(f"✅ 可见Gaussian: {visible_gaussians}")
        print(f"✅ 最大颜色值: {max_color:.6f}")
        
        if max_color > 0.01:
            print("🎉 训练成功！渲染效果良好！")
            
            # 保存最终渲染结果
            import imageio
            render_img = render_colors[0].detach().cpu().numpy()
            render_img_uint8 = (np.clip(render_img, 0, 1) * 255).astype(np.uint8)
            imageio.imwrite(f"{cfg.result_dir}/final_render.png", render_img_uint8)
            print(f"✅ 保存最终渲染到 {cfg.result_dir}/final_render.png")
        else:
            print("⚠️ 渲染仍然较暗，可能需要进一步调整")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")


if __name__ == "__main__":
    main()
