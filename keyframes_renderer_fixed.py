#!/usr/bin/env python3
"""
修复的关键帧渲染器 - 与训练参数完全一致
解决渲染效果不好的问题
"""

import os
import numpy as np
import torch
import imageio
import tqdm
import yaml
from typing import Optional, Tuple, List
from dataclasses import dataclass
import tyro

from keyframes_dataset import KeyframesDataset, KeyframesParser
from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
from gsplat.rendering import rasterization


@dataclass
class FixedRenderConfig:
    """修复的渲染配置 - 与训练参数完全一致"""
    
    # Model settings
    ckpt_path: str  # Path to checkpoint file
    result_dir: str = "results/render_fixed"  # Output directory
    
    # Rendering settings
    render_mode: str = "turntable"  # "turntable", "spiral", "orbit", "keyframes"
    num_frames: int = 60  # Number of frames
    radius: float = 2.0  # Camera distance from center
    height_offset: float = 0.0  # Height offset for camera
    
    # Camera settings (从训练数据获取)
    width: int = 512
    height: int = 288
    fov: float = 60.0  # Field of view in degrees
    
    # Quality settings
    use_training_params: bool = True  # 使用与训练完全一致的参数
    gaussian_filter_threshold: float = 0.005  # 过滤低透明度的Gaussian
    max_gaussians: int = 2000000  # 最大Gaussian数量
    
    # Output settings
    save_video: bool = False  # 默认不保存视频，避免编码问题
    fps: int = 30
    
    # Device
    device: str = "cuda"


class FixedKeyframesRenderer:
    """修复的关键帧渲染器 - 与训练参数完全一致"""
    
    def __init__(self, config: FixedRenderConfig):
        self.config = config
        self.device = config.device
        
        # 创建输出目录
        os.makedirs(config.result_dir, exist_ok=True)
        
        # 加载checkpoint和训练配置
        self.load_model()
        
        print(f"✅ 修复渲染器初始化完成")
        print(f"   - Gaussians数量: {self.num_gaussians:,}")
        print(f"   - 训练步数: {self.training_step}")
        print(f"   - SH degree: {self.sh_degree}")
        print(f"   - 渲染模式: {self.rasterize_mode}")
    
    def load_model(self):
        """加载模型和训练配置"""
        print(f"加载checkpoint: {self.config.ckpt_path}")
        
        # 加载checkpoint
        ckpt = torch.load(self.config.ckpt_path, map_location=self.device, weights_only=False)
        
        # 获取训练配置
        cfg_path = os.path.join(os.path.dirname(self.config.ckpt_path), "..", "cfg.yml")
        if os.path.exists(cfg_path):
            try:
                with open(cfg_path, 'r') as f:
                    # 读取文件内容并过滤掉Python对象
                    content = f.read()
                    lines = content.split('\n')
                    filtered_lines = []
                    skip_next = False

                    for line in lines:
                        if 'python/object:' in line:
                            skip_next = True
                            continue
                        if skip_next and line.startswith('  '):
                            continue
                        skip_next = False
                        filtered_lines.append(line)

                    filtered_content = '\n'.join(filtered_lines)
                    self.training_cfg = yaml.safe_load(filtered_content)
                print(f"✅ 加载训练配置: {cfg_path}")
            except Exception as e:
                print(f"⚠️  加载配置文件失败: {e}，使用默认参数")
                self.training_cfg = {}
        else:
            print(f"⚠️  未找到训练配置文件，使用默认参数")
            self.training_cfg = {}
        
        # 从checkpoint文件名获取训练步数
        ckpt_filename = os.path.basename(self.config.ckpt_path)
        if "ckpt_" in ckpt_filename:
            try:
                # 处理 ckpt_29999.pt 或 ckpt_29999_optimized.pt 格式
                step_part = ckpt_filename.split("ckpt_")[1].split(".")[0]
                if "_" in step_part:
                    step_part = step_part.split("_")[0]  # 取第一部分作为步数
                self.training_step = int(step_part)
            except (ValueError, IndexError):
                self.training_step = 30000  # 默认值
        else:
            self.training_step = 30000  # 默认值
        
        # 计算正确的SH degree (与训练时一致)
        sh_degree_interval = self.training_cfg.get('sh_degree_interval', 1000)
        max_sh_degree = self.training_cfg.get('sh_degree', 3)
        self.sh_degree = min(self.training_step // sh_degree_interval, max_sh_degree)
        
        # 获取训练时的渲染参数
        self.antialiased = self.training_cfg.get('antialiased', False)
        self.rasterize_mode = "antialiased" if self.antialiased else "classic"
        self.packed = self.training_cfg.get('packed', False)
        self.near_plane = self.training_cfg.get('near_plane', 0.01)
        self.far_plane = self.training_cfg.get('far_plane', 1e10)
        
        # 直接使用checkpoint中的splats
        self.splats = ckpt["splats"]
        self.num_gaussians = self.splats["means"].shape[0]
        
        # 移动到GPU
        for key in self.splats:
            if isinstance(self.splats[key], torch.Tensor):
                self.splats[key] = self.splats[key].to(self.device)
        
        # 加载数据集用于获取相机参数
        data_path = self.training_cfg.get('data_path', 'data/keyframes')
        if os.path.isdir(data_path):
            # 多文件模式，使用第一个文件
            pkl_files = [f for f in os.listdir(data_path) if f.endswith('.pkl')]
            if pkl_files:
                data_path = os.path.join(data_path, sorted(pkl_files)[0])
        
        self.dataset = KeyframesDataset(
            data_path=data_path,
            confidence_threshold=0.0,
            device=self.device
        )
        
        print(f"✅ 模型加载完成")
    
    def filter_gaussians(self) -> dict:
        """过滤Gaussian以提高渲染质量"""
        if not self.config.use_training_params:
            return self.splats
        
        print(f"过滤Gaussian...")
        
        # 计算透明度
        opacities = torch.sigmoid(self.splats["opacities"])
        
        # 过滤低透明度的Gaussian
        valid_mask = opacities > self.config.gaussian_filter_threshold
        
        # 如果还是太多，选择透明度最高的
        if valid_mask.sum() > self.config.max_gaussians:
            _, indices = torch.topk(opacities, self.config.max_gaussians)
            valid_mask = torch.zeros_like(opacities, dtype=torch.bool)
            valid_mask[indices] = True
        
        # 创建过滤后的splats
        filtered_splats = {}
        for key, value in self.splats.items():
            if isinstance(value, torch.Tensor) and len(value) == len(opacities):
                filtered_splats[key] = value[valid_mask]
            else:
                filtered_splats[key] = value
        
        num_filtered = valid_mask.sum().item()
        print(f"✅ 过滤完成: {self.num_gaussians:,} -> {num_filtered:,} Gaussians")
        
        return filtered_splats
    
    def rasterize_splats(
        self,
        camtoworld: torch.Tensor,
        K: torch.Tensor,
        width: int,
        height: int,
        use_filtered: bool = True,
    ) -> torch.Tensor:
        """使用与训练完全一致的参数进行渲染"""
        
        # 选择使用的splats
        splats = self.filter_gaussians() if use_filtered else self.splats
        
        # 准备渲染参数
        means = splats["means"]
        quats = splats["quats"]
        scales = torch.exp(splats["scales"])
        opacities = torch.sigmoid(splats["opacities"])
        colors = torch.cat([splats["sh0"], splats["shN"]], 1)
        
        # 使用与训练完全一致的参数进行渲染
        render_colors, _, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=width,
            height=height,
            sh_degree=self.sh_degree,  # 使用训练时的SH degree
            packed=self.packed,  # 使用训练时的packed设置
            rasterize_mode=self.rasterize_mode,  # 使用训练时的渲染模式
            near_plane=self.near_plane,  # 使用训练时的near plane
            far_plane=self.far_plane,  # 使用训练时的far plane
        )
        
        return render_colors[0], info  # Remove batch dimension
    
    def generate_camera_poses(self, mode: str, num_frames: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """生成相机轨迹"""
        # 获取参考相机参数
        data_item = self.dataset[0]
        ref_pose = data_item['camtoworld'][0]  # [4, 4]
        K = data_item['K'][0]  # [3, 3]
        
        poses = []
        
        if mode == "keyframes":
            # 渲染所有关键帧
            for i in range(min(len(self.dataset), num_frames)):
                data_item = self.dataset[i]
                pose = data_item['camtoworld'][0]
                poses.append(pose)
        elif mode == "turntable":
            # 围绕参考点旋转
            center = ref_pose[:3, 3]  # 参考相机位置作为中心
            
            for i in range(num_frames):
                angle = 2 * np.pi * i / num_frames
                
                # 创建旋转矩阵
                R = torch.eye(4, device=self.device)
                R[0, 0] = np.cos(angle)
                R[0, 2] = np.sin(angle)
                R[2, 0] = -np.sin(angle)
                R[2, 2] = np.cos(angle)
                
                # 应用旋转
                pose = ref_pose.clone()
                pose[:3, 3] = center + self.config.radius * (R[:3, :3] @ (pose[:3, 3] - center))
                pose[:3, :3] = R[:3, :3] @ pose[:3, :3]
                
                poses.append(pose)
        else:
            # 默认使用参考pose
            poses = [ref_pose] * num_frames
        
        return poses, K

    def render_trajectory(self):
        """渲染相机轨迹"""
        config = self.config

        print(f"生成 {config.render_mode} 轨迹，共 {config.num_frames} 帧...")
        poses, K = self.generate_camera_poses(config.render_mode, config.num_frames)

        # 渲染帧
        frames = []
        render_stats = []

        for i, pose in enumerate(tqdm.tqdm(poses, desc="渲染中")):
            with torch.no_grad():
                rendered, info = self.rasterize_splats(
                    camtoworld=pose,
                    K=K,
                    width=config.width,
                    height=config.height,
                )

                # 转换为numpy并限制范围
                frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
                frame = (frame * 255).astype(np.uint8)
                frames.append(frame)

                # 记录渲染统计
                visible_gaussians = (info['radii'] > 0).sum().item()
                render_stats.append({
                    'frame': i,
                    'visible_gaussians': visible_gaussians,
                    'mean_color': rendered.mean().item(),
                    'max_color': rendered.max().item()
                })

                # 保存单帧
                imageio.imwrite(
                    os.path.join(config.result_dir, f"frame_{i:04d}.png"),
                    frame
                )

        # 保存视频
        if config.save_video:
            video_path = os.path.join(config.result_dir, f"{config.render_mode}_fixed.mp4")
            try:
                # 尝试使用ffmpeg编码器
                imageio.mimsave(video_path, frames, fps=config.fps, codec='libx264')
                print(f"✅ 视频保存至: {video_path}")
            except Exception as e:
                print(f"⚠️  MP4保存失败: {e}")
                # 尝试保存为GIF
                gif_path = os.path.join(config.result_dir, f"{config.render_mode}_fixed.gif")
                try:
                    imageio.mimsave(gif_path, frames, fps=config.fps)
                    print(f"✅ GIF保存至: {gif_path}")
                except Exception as e2:
                    print(f"❌ 视频保存失败: {e2}")
                    print("   单帧图像已保存，可手动合成视频")

        # 保存渲染统计
        import json
        stats_path = os.path.join(config.result_dir, "render_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(render_stats, f, indent=2)

        print(f"✅ 渲染完成: {len(frames)} 帧保存至 {config.result_dir}")
        print(f"   平均可见Gaussians: {np.mean([s['visible_gaussians'] for s in render_stats]):,.0f}")

        return frames

    def render_single_view(self, pose: torch.Tensor, K: torch.Tensor, output_path: str):
        """渲染单个视角"""
        with torch.no_grad():
            rendered, info = self.rasterize_splats(
                camtoworld=pose,
                K=K,
                width=self.config.width,
                height=self.config.height,
            )

            frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
            frame = (frame * 255).astype(np.uint8)

            imageio.imwrite(output_path, frame)

            visible_gaussians = (info['radii'] > 0).sum().item()
            print(f"✅ 视角渲染完成: {output_path}")
            print(f"   可见Gaussians: {visible_gaussians:,}")
            print(f"   颜色范围: [{rendered.min():.3f}, {rendered.max():.3f}]")

    def render_keyframes_comparison(self):
        """渲染关键帧对比（训练图像 vs 渲染结果）"""
        print("渲染关键帧对比...")

        for i in range(min(len(self.dataset), 10)):  # 最多10帧
            data_item = self.dataset[i]
            pose = data_item['camtoworld'][0]
            K = data_item['K'][0]
            gt_image = data_item['image'][0].permute(1, 2, 0).cpu().numpy()  # [H, W, 3]

            # 获取实际的图像尺寸
            actual_height, actual_width = gt_image.shape[:2]

            with torch.no_grad():
                rendered, info = self.rasterize_splats(
                    camtoworld=pose,
                    K=K,
                    width=actual_width,
                    height=actual_height,
                )

                rendered_np = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()

                # 确保尺寸匹配
                if rendered_np.shape != gt_image.shape:
                    print(f"  警告: 尺寸不匹配 GT:{gt_image.shape} vs 渲染:{rendered_np.shape}")
                    # 调整渲染图像尺寸
                    if rendered_np.shape[0] != gt_image.shape[0] or rendered_np.shape[1] != gt_image.shape[1]:
                        import cv2
                        rendered_np = cv2.resize(rendered_np, (gt_image.shape[1], gt_image.shape[0]))

                # 创建对比图像
                comparison = np.concatenate([gt_image, rendered_np], axis=1)
                comparison = (comparison * 255).astype(np.uint8)

                # 保存对比图像
                output_path = os.path.join(self.config.result_dir, f"comparison_{i:04d}.png")
                imageio.imwrite(output_path, comparison)

                # 单独保存渲染结果
                render_path = os.path.join(self.config.result_dir, f"rendered_{i:04d}.png")
                render_img = (rendered_np * 255).astype(np.uint8)
                imageio.imwrite(render_path, render_img)

                visible_gaussians = (info['radii'] > 0).sum().item()
                print(f"  帧 {i}: 可见Gaussians {visible_gaussians:,}, 颜色范围 [{rendered.min():.3f}, {rendered.max():.3f}]")

        print(f"✅ 关键帧对比完成，保存至 {self.config.result_dir}")


def main():
    """主函数"""
    config = tyro.cli(FixedRenderConfig)

    print("=== 修复的关键帧渲染器 ===")
    print(f"配置: {config}")

    renderer = FixedKeyframesRenderer(config)

    if config.render_mode == "keyframes":
        renderer.render_keyframes_comparison()
    else:
        renderer.render_trajectory()

    print("✅ 渲染完成!")


if __name__ == "__main__":
    main()
