#!/usr/bin/env python3
"""
诊断渲染问题的脚本
"""

import torch
import numpy as np
import imageio
from keyframes_dataset import KeyframesDataset, KeyframesParser
from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
from gsplat.rendering import rasterization


def diagnose_rendering_pipeline():
    """诊断完整的渲染流程"""
    print("=== 诊断渲染流程 ===")

    # 1. 加载训练好的模型
    try:
        ckpt = torch.load("results/multi_keyframes/ckpts/ckpt_29999.pt", map_location="cuda", weights_only=False)
        print("✅ 成功加载检查点")
    except Exception as e:
        print(f"❌ 加载检查点失败: {e}")
        return

    # 2. 直接使用checkpoint中的splats，不重建模型
    splats = ckpt["splats"]
    print("✅ 成功加载模型参数")

    # 3. 加载数据集获取相机参数
    dataset = KeyframesDataset(
        data_path="data/keyframes/24.pkl",
        confidence_threshold=0.0,
        device="cuda"
    )
    
    # 4. 获取相机参数
    data_item = dataset[0]
    camtoworld = data_item['camtoworld'][0]  # [4, 4]
    K = data_item['K'][0]  # [3, 3]
    target_image = data_item['image'][0]  # [3, H, W]
    
    print(f"相机位姿形状: {camtoworld.shape}")
    print(f"相机内参形状: {K.shape}")
    print(f"目标图像形状: {target_image.shape}")
    
    # 5. 检查模型参数
    means = splats["means"]
    quats = splats["quats"]
    scales = torch.exp(splats["scales"])
    opacities = torch.sigmoid(splats["opacities"])
    colors = torch.cat([splats["sh0"], splats["shN"]], 1)
    
    print(f"\n=== 模型参数检查 ===")
    print(f"Gaussian数量: {len(means)}")
    print(f"位置范围: X[{means[:, 0].min():.3f}, {means[:, 0].max():.3f}]")
    print(f"位置范围: Y[{means[:, 1].min():.3f}, {means[:, 1].max():.3f}]")
    print(f"位置范围: Z[{means[:, 2].min():.3f}, {means[:, 2].max():.3f}]")
    print(f"透明度范围: [{opacities.min():.3f}, {opacities.max():.3f}]")
    print(f"尺度范围: [{scales.min():.3f}, {scales.max():.3f}]")
    print(f"颜色范围: [{colors.min():.3f}, {colors.max():.3f}]")
    
    # 6. 检查相机参数
    print(f"\n=== 相机参数检查 ===")
    print(f"相机位置: {camtoworld[:3, 3]}")
    print(f"相机内参K:\n{K}")
    
    # 7. 执行渲染
    print(f"\n=== 执行渲染 ===")
    try:
        render_colors, render_alphas, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=512,
            height=288,
            sh_degree=3,
        )
        
        print("✅ 渲染成功")
        print(f"渲染图像形状: {render_colors.shape}")
        print(f"渲染图像范围: [{render_colors.min():.6f}, {render_colors.max():.6f}]")
        print(f"渲染图像均值: {render_colors.mean():.6f}")
        print(f"渲染alpha形状: {render_alphas.shape}")
        print(f"渲染alpha范围: [{render_alphas.min():.6f}, {render_alphas.max():.6f}]")
        print(f"可见Gaussian数量: {(info['radii'] > 0).sum().item()}")
        
        # 8. 保存渲染结果
        render_img = render_colors[0].detach().cpu().numpy()  # [H, W, 3]
        render_img_uint8 = (np.clip(render_img, 0, 1) * 255).astype(np.uint8)
        imageio.imwrite("debug_render.png", render_img_uint8)
        print("✅ 保存渲染图像到 debug_render.png")
        
        # 9. 保存目标图像用于对比
        target_img = target_image.permute(1, 2, 0).detach().cpu().numpy()  # [H, W, 3]
        target_img_uint8 = (np.clip(target_img, 0, 1) * 255).astype(np.uint8)
        imageio.imwrite("debug_target.png", target_img_uint8)
        print("✅ 保存目标图像到 debug_target.png")
        
        # 10. 分析渲染问题
        if render_colors.max() < 1e-6:
            print("❌ 渲染图像几乎全黑")
            diagnose_black_rendering(means, camtoworld, K, opacities, scales, colors)
        else:
            print("✅ 渲染图像有内容")
            
    except Exception as e:
        print(f"❌ 渲染失败: {e}")
        import traceback
        traceback.print_exc()


def diagnose_black_rendering(means, camtoworld, K, opacities, scales, colors):
    """诊断黑色渲染的具体原因"""
    print("\n=== 诊断黑色渲染原因 ===")
    
    # 1. 检查Gaussian是否在相机视野内
    # 将3D点投影到相机坐标系
    viewmat = torch.linalg.inv(camtoworld.unsqueeze(0))  # [1, 4, 4]
    means_hom = torch.cat([means, torch.ones(len(means), 1, device=means.device)], dim=1)  # [N, 4]
    means_cam = (viewmat[0] @ means_hom.T).T[:, :3]  # [N, 3]
    
    # 检查深度
    depths = means_cam[:, 2]
    print(f"深度范围: [{depths.min():.3f}, {depths.max():.3f}]")
    print(f"正深度点数量: {(depths > 0).sum().item()}/{len(depths)}")
    
    if (depths > 0).sum() == 0:
        print("❌ 所有点都在相机后面！")
        return
    
    # 2. 投影到图像平面
    valid_mask = depths > 0.01  # 过滤太近的点
    if valid_mask.sum() == 0:
        print("❌ 没有有效的点可以投影！")
        return
        
    valid_means_cam = means_cam[valid_mask]
    valid_opacities = opacities[valid_mask]
    valid_scales = scales[valid_mask]
    
    # 投影到像素坐标
    points_2d = (K @ valid_means_cam.T).T  # [N, 3]
    points_2d = points_2d[:, :2] / points_2d[:, 2:3]  # [N, 2]
    
    # 检查是否在图像范围内
    in_image = (
        (points_2d[:, 0] >= 0) & (points_2d[:, 0] < 512) &
        (points_2d[:, 1] >= 0) & (points_2d[:, 1] < 288)
    )
    
    print(f"投影到图像内的点数量: {in_image.sum().item()}/{len(points_2d)}")
    
    if in_image.sum() == 0:
        print("❌ 没有点投影到图像内！")
        print(f"投影点X范围: [{points_2d[:, 0].min():.1f}, {points_2d[:, 0].max():.1f}]")
        print(f"投影点Y范围: [{points_2d[:, 1].min():.1f}, {points_2d[:, 1].max():.1f}]")
        return
    
    # 3. 检查可见点的属性
    visible_points = valid_means_cam[in_image]
    visible_opacities = valid_opacities[in_image]
    visible_scales = valid_scales[in_image]
    
    print(f"可见点透明度范围: [{visible_opacities.min():.3f}, {visible_opacities.max():.3f}]")
    print(f"可见点尺度范围: [{visible_scales.min():.3f}, {visible_scales.max():.3f}]")
    print(f"可见点深度范围: [{visible_points[:, 2].min():.3f}, {visible_points[:, 2].max():.3f}]")
    
    # 4. 检查颜色
    if len(colors.shape) == 3:  # [N, K, 3]
        # 只检查第0阶球谐函数（基础颜色）
        base_colors = colors[:, 0, :]  # [N, 3]
        visible_colors = base_colors[valid_mask][in_image]
        print(f"可见点颜色范围: [{visible_colors.min():.3f}, {visible_colors.max():.3f}]")
        print(f"可见点颜色均值: {visible_colors.mean():.3f}")
        
        # 转换SH到RGB
        C0 = 0.28209479177387814
        rgb_colors = visible_colors * C0 + 0.5
        print(f"转换后RGB范围: [{rgb_colors.min():.3f}, {rgb_colors.max():.3f}]")
        
        if rgb_colors.max() < 0.1:
            print("❌ 颜色太暗！")
        elif rgb_colors.min() > 0.9:
            print("❌ 颜色太亮！")


def test_simple_rendering():
    """测试简化的渲染"""
    print("\n=== 测试简化渲染 ===")
    
    # 创建简单的测试场景
    device = "cuda"
    
    # 简单的3D点
    means = torch.tensor([
        [0.0, 0.0, 1.0],  # 相机前方1米
        [0.5, 0.0, 1.0],  # 右侧
        [-0.5, 0.0, 1.0], # 左侧
    ], device=device)
    
    # 简单的参数
    quats = torch.tensor([
        [1.0, 0.0, 0.0, 0.0],
        [1.0, 0.0, 0.0, 0.0],
        [1.0, 0.0, 0.0, 0.0],
    ], device=device)
    
    scales = torch.tensor([
        [0.1, 0.1, 0.1],
        [0.1, 0.1, 0.1],
        [0.1, 0.1, 0.1],
    ], device=device)
    
    opacities = torch.tensor([0.8, 0.8, 0.8], device=device)
    
    # 明亮的白色
    colors = torch.zeros(3, 16, 3, device=device)  # [N, K, 3]
    colors[:, 0, :] = 1.0  # 第0阶设为白色
    
    # 简单的相机
    camtoworld = torch.eye(4, device=device)
    K = torch.tensor([
        [500.0, 0.0, 256.0],
        [0.0, 500.0, 144.0],
        [0.0, 0.0, 1.0]
    ], device=device)
    
    try:
        render_colors, _, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=512,
            height=288,
            sh_degree=3,
        )
        
        print(f"简化渲染结果: [{render_colors.min():.3f}, {render_colors.max():.3f}]")
        print(f"可见Gaussian: {(info['radii'] > 0).sum().item()}")
        
        if render_colors.max() > 0.1:
            print("✅ 简化渲染成功！问题可能在模型参数")
        else:
            print("❌ 简化渲染也失败，可能是渲染管道问题")
            
    except Exception as e:
        print(f"❌ 简化渲染失败: {e}")


def main():
    """主诊断函数"""
    print("🔍 开始诊断渲染问题...")
    
    diagnose_rendering_pipeline()
    test_simple_rendering()
    
    print("\n🎯 可能的解决方案:")
    print("1. 如果点在相机后面：调整相机位姿或点云位置")
    print("2. 如果点不在视野内：调整相机内参或点云范围")
    print("3. 如果颜色太暗：调整SH系数或颜色初始化")
    print("4. 如果尺度太小：增加Gaussian尺度")
    print("5. 如果透明度太低：增加透明度")


if __name__ == "__main__":
    main()
