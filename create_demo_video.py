#!/usr/bin/env python3
"""
Create a demo video from evaluation images.
"""

import os
import glob
import imageio
import numpy as np
from PIL import Image

def create_demo_video(render_dir: str, output_path: str, step: int = 29999):
    """Create a demo video from evaluation renders."""
    
    # Find all evaluation images for the specified step
    pattern = os.path.join(render_dir, f"eval_step{step}_*.png")
    image_files = sorted(glob.glob(pattern))
    
    if not image_files:
        print(f"No images found for step {step}")
        return
    
    print(f"Found {len(image_files)} images for step {step}")
    
    # Load images
    frames = []
    for img_file in image_files:
        img = imageio.imread(img_file)
        frames.append(img)
        print(f"Loaded: {os.path.basename(img_file)} - Shape: {img.shape}")
    
    # Create video by repeating the sequence multiple times
    # and adding some transitions
    video_frames = []
    
    # Add forward sequence
    for frame in frames:
        video_frames.append(frame)
    
    # Add reverse sequence for smooth loop
    for frame in reversed(frames[1:-1]):  # Skip first and last to avoid duplication
        video_frames.append(frame)
    
    # Repeat the sequence a few times
    final_frames = []
    for _ in range(3):  # Repeat 3 times
        final_frames.extend(video_frames)
    
    # Save video
    print(f"Creating video with {len(final_frames)} frames...")
    try:
        # Try with different formats
        if output_path.endswith('.mp4'):
            imageio.mimsave(output_path, final_frames, fps=10, format='mp4', codec='libx264')
        else:
            # Fallback to GIF
            gif_path = output_path.replace('.mp4', '.gif')
            imageio.mimsave(gif_path, final_frames, fps=5, format='gif')
            print(f"Demo GIF saved to: {gif_path}")
            return
    except Exception as e:
        print(f"MP4 creation failed: {e}")
        # Fallback to GIF
        gif_path = output_path.replace('.mp4', '.gif')
        imageio.mimsave(gif_path, final_frames, fps=5, format='gif')
        print(f"Demo GIF saved to: {gif_path}")
        return

    print(f"Demo video saved to: {output_path}")

def create_comparison_grid(render_dir: str, output_path: str, step: int = 29999):
    """Create a comparison grid showing all views."""
    
    # Find all evaluation images for the specified step
    pattern = os.path.join(render_dir, f"eval_step{step}_*.png")
    image_files = sorted(glob.glob(pattern))
    
    if not image_files:
        print(f"No images found for step {step}")
        return
    
    print(f"Creating comparison grid with {len(image_files)} images")
    
    # Load images
    images = []
    for img_file in image_files:
        img = Image.open(img_file)
        images.append(img)
    
    # Calculate grid size
    n_images = len(images)
    cols = min(4, n_images)  # Max 4 columns
    rows = (n_images + cols - 1) // cols
    
    # Get image size
    img_width, img_height = images[0].size
    
    # Create grid
    grid_width = cols * img_width
    grid_height = rows * img_height
    grid = Image.new('RGB', (grid_width, grid_height), color='white')
    
    # Place images in grid
    for i, img in enumerate(images):
        row = i // cols
        col = i % cols
        x = col * img_width
        y = row * img_height
        grid.paste(img, (x, y))
    
    # Save grid
    grid.save(output_path)
    print(f"Comparison grid saved to: {output_path}")

def main():
    """Main function."""
    render_dir = "results/multi_keyframes/renders"
    output_dir = "results/demo"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Create demo video for the final step
    create_demo_video(
        render_dir=render_dir,
        output_path=os.path.join(output_dir, "demo_step29999.mp4"),
        step=29999
    )
    
    # Create comparison grid
    create_comparison_grid(
        render_dir=render_dir,
        output_path=os.path.join(output_dir, "comparison_step29999.png"),
        step=29999
    )
    
    # Also create for step 9999 for comparison
    create_demo_video(
        render_dir=render_dir,
        output_path=os.path.join(output_dir, "demo_step9999.mp4"),
        step=9999
    )
    
    create_comparison_grid(
        render_dir=render_dir,
        output_path=os.path.join(output_dir, "comparison_step9999.png"),
        step=9999
    )
    
    print("Demo creation completed!")

if __name__ == "__main__":
    main()
