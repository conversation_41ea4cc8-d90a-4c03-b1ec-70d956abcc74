#!/usr/bin/env python3
"""
Memory-optimized renderer for trained 3DGS models.
"""

import os
import numpy as np
import torch
import imageio
import tqdm
from typing import Tuple, List
from dataclasses import dataclass
import tyro

from gsplat.rendering import rasterization


@dataclass
class OptimizedRenderConfig:
    """Configuration for optimized rendering."""
    
    # Model settings
    ckpt_path: str  # Path to checkpoint file
    result_dir: str = "results/render"  # Output directory
    
    # Rendering settings
    render_mode: str = "turntable"  # "turntable", "spiral", "orbit"
    num_frames: int = 60  # Number of frames
    radius: float = 2.0  # Camera distance from center
    height_offset: float = 0.0  # Height offset for camera
    
    # Camera settings (corrected: keyframes are 512 height x 288 width)
    width: int = 288
    height: int = 512
    fov: float = 60.0  # Field of view in degrees
    
    # Memory optimization
    chunk_size: int = 100000  # Process Gaussians in chunks
    low_memory: bool = True  # Enable memory optimizations
    
    # Output settings
    save_video: bool = True
    fps: int = 30
    
    # Device
    device: str = "cuda"


class OptimizedKeyframesRenderer:
    """Memory-optimized renderer for trained 3DGS models."""
    
    def __init__(self, config: OptimizedRenderConfig):
        self.config = config
        self.device = config.device
        
        # Create output directory
        os.makedirs(config.result_dir, exist_ok=True)
        
        # Load checkpoint
        print(f"Loading checkpoint from: {config.ckpt_path}")
        ckpt = torch.load(config.ckpt_path, map_location=self.device, weights_only=False)
        
        # Load splats directly from checkpoint
        self.splats = {}
        for key, value in ckpt["splats"].items():
            self.splats[key] = value.to(self.device)
        
        self.scene_scale = ckpt.get("scene_scale", 1.0)
        self.num_gaussians = len(self.splats["means"])
        
        print(f"Model loaded with {self.num_gaussians} Gaussians")
        print(f"Scene scale: {self.scene_scale}")
        
        # Memory optimization: move to CPU if low memory mode
        if config.low_memory and self.num_gaussians > 1000000:
            print("Low memory mode: moving splats to CPU")
            for key in self.splats:
                self.splats[key] = self.splats[key].cpu()
    
    def get_camera_intrinsics(self, width: int, height: int, fov: float) -> torch.Tensor:
        """Generate camera intrinsics matrix."""
        fov_rad = np.deg2rad(fov)
        fx = fy = width / (2.0 * np.tan(fov_rad / 2.0))
        cx = width / 2.0
        cy = height / 2.0
        
        K = torch.tensor([
            [fx, 0.0, cx],
            [0.0, fy, cy],
            [0.0, 0.0, 1.0]
        ], device=self.device, dtype=torch.float32)
        
        return K
    
    def rasterize_splats_chunked(
        self,
        camtoworld: torch.Tensor,
        K: torch.Tensor,
        width: int,
        height: int,
        **kwargs,
    ) -> torch.Tensor:
        """Rasterize splats with memory optimization."""
        
        if self.config.low_memory and self.num_gaussians > self.config.chunk_size:
            # Process in chunks to save memory
            return self._rasterize_chunked(camtoworld, K, width, height, **kwargs)
        else:
            # Standard rasterization
            return self._rasterize_standard(camtoworld, K, width, height, **kwargs)
    
    def _rasterize_standard(self, camtoworld, K, width, height, **kwargs):
        """Standard rasterization."""
        # Move splats to GPU if needed
        means = self.splats["means"].to(self.device)
        quats = self.splats["quats"].to(self.device)
        scales = torch.exp(self.splats["scales"].to(self.device))
        opacities = torch.sigmoid(self.splats["opacities"].to(self.device))
        colors = torch.cat([
            self.splats["sh0"].to(self.device), 
            self.splats["shN"].to(self.device)
        ], 1)
        
        render_colors, _, _ = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=width,
            height=height,
            sh_degree=3,
            packed=False,
            rasterize_mode="antialiased",
            **kwargs,
        )
        
        return render_colors[0]  # Remove batch dimension
    
    def _rasterize_chunked(self, camtoworld, K, width, height, **kwargs):
        """Chunked rasterization for memory efficiency."""
        print("Using memory-optimized rendering...")

        # Try to use more gaussians for better quality
        max_gaussians = min(2000000, self.num_gaussians)  # Increase to 2M gaussians

        # Select most important gaussians (by opacity)
        opacities_cpu = torch.sigmoid(self.splats["opacities"])
        _, indices = torch.topk(opacities_cpu, min(max_gaussians, len(opacities_cpu)))

        # Move selected gaussians to GPU
        means = self.splats["means"][indices].to(self.device)
        quats = self.splats["quats"][indices].to(self.device)
        scales = torch.exp(self.splats["scales"][indices].to(self.device))
        opacities = torch.sigmoid(self.splats["opacities"][indices].to(self.device))
        colors = torch.cat([
            self.splats["sh0"][indices].to(self.device),
            self.splats["shN"][indices].to(self.device)
        ], 1)

        # Use the same rendering parameters as training
        render_colors, _, _ = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=width,
            height=height,
            sh_degree=3,  # Use full SH degree like in training
            packed=False,  # Match training settings
            rasterize_mode="antialiased",  # Use antialiased mode
            **kwargs,
        )

        # Clear GPU memory
        del means, quats, scales, opacities, colors
        torch.cuda.empty_cache()

        return render_colors[0]  # Remove batch dimension
    
    def generate_camera_trajectory(self, mode: str, num_frames: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """Generate camera trajectory."""
        poses = []
        K = self.get_camera_intrinsics(self.config.width, self.config.height, self.config.fov)
        
        if mode == "turntable":
            # Circular trajectory around scene center
            scene_center = torch.tensor([0.0, 0.0, 0.0], device=self.device)
            radius = self.config.radius
            height = self.config.height_offset
            
            for i in range(num_frames):
                angle = 2 * np.pi * i / num_frames
                
                # Camera position
                x = scene_center[0] + radius * np.cos(angle)
                y = scene_center[1] + radius * np.sin(angle)
                z = height
                cam_pos = torch.tensor([x, y, z], device=self.device, dtype=torch.float32)
                
                # Look at scene center
                forward = scene_center - cam_pos
                forward = forward / torch.norm(forward)
                
                # Up vector
                up = torch.tensor([0.0, 0.0, 1.0], device=self.device, dtype=torch.float32)
                right = torch.cross(forward, up)
                right = right / torch.norm(right)
                up = torch.cross(right, forward)
                
                # Create rotation matrix
                R = torch.stack([right, up, -forward], dim=1)
                
                # Create pose matrix
                pose = torch.eye(4, device=self.device)
                pose[:3, :3] = R
                pose[:3, 3] = cam_pos
                
                poses.append(pose)
        
        elif mode == "spiral":
            # Spiral trajectory
            for i in range(num_frames):
                t = i / num_frames
                angle = 4 * np.pi * t  # 2 full rotations
                radius = self.config.radius * (1 + 0.3 * np.sin(2 * np.pi * t))
                height = self.config.height_offset + 0.5 * np.sin(4 * np.pi * t)
                
                x = radius * np.cos(angle)
                y = radius * np.sin(angle)
                z = height
                cam_pos = torch.tensor([x, y, z], device=self.device, dtype=torch.float32)
                
                # Look at origin
                forward = -cam_pos / torch.norm(cam_pos)
                up = torch.tensor([0.0, 0.0, 1.0], device=self.device, dtype=torch.float32)
                right = torch.cross(forward, up)
                right = right / torch.norm(right)
                up = torch.cross(right, forward)
                
                R = torch.stack([right, up, -forward], dim=1)
                pose = torch.eye(4, device=self.device)
                pose[:3, :3] = R
                pose[:3, 3] = cam_pos
                
                poses.append(pose)
        
        elif mode == "orbit":
            # Orbital trajectory with varying height
            for i in range(num_frames):
                t = i / num_frames
                angle = 2 * np.pi * t
                elevation = 0.3 * np.sin(4 * np.pi * t)  # Varying elevation
                
                radius = self.config.radius
                x = radius * np.cos(angle)
                y = radius * np.sin(angle)
                z = self.config.height_offset + elevation
                cam_pos = torch.tensor([x, y, z], device=self.device, dtype=torch.float32)
                
                # Look at origin
                forward = -cam_pos / torch.norm(cam_pos)
                up = torch.tensor([0.0, 0.0, 1.0], device=self.device, dtype=torch.float32)
                right = torch.cross(forward, up)
                right = right / torch.norm(right)
                up = torch.cross(right, forward)
                
                R = torch.stack([right, up, -forward], dim=1)
                pose = torch.eye(4, device=self.device)
                pose[:3, :3] = R
                pose[:3, 3] = cam_pos
                
                poses.append(pose)
        
        return poses, K
    
    def render_trajectory(self):
        """Render camera trajectory."""
        config = self.config
        
        print(f"Generating {config.render_mode} trajectory with {config.num_frames} frames...")
        poses, K = self.generate_camera_trajectory(config.render_mode, config.num_frames)
        
        # Render frames
        frames = []
        for i, pose in enumerate(tqdm.tqdm(poses, desc="Rendering")):
            with torch.no_grad():
                rendered = self.rasterize_splats_chunked(
                    camtoworld=pose,
                    K=K,
                    width=config.width,
                    height=config.height,
                )
                
                # Convert to numpy and clamp
                frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
                frame = (frame * 255).astype(np.uint8)
                frames.append(frame)
                
                # Save individual frame
                imageio.imwrite(
                    os.path.join(config.result_dir, f"frame_{i:04d}.png"),
                    frame
                )
        
        # Save video if requested
        if config.save_video:
            try:
                video_path = os.path.join(config.result_dir, f"{config.render_mode}.mp4")
                imageio.mimsave(video_path, frames, fps=config.fps)
                print(f"Video saved to: {video_path}")
            except Exception as e:
                print(f"MP4 creation failed: {e}")
                # Fallback to GIF
                gif_path = os.path.join(config.result_dir, f"{config.render_mode}.gif")
                imageio.mimsave(gif_path, frames, fps=min(config.fps, 10))
                print(f"GIF saved to: {gif_path}")
        
        print(f"Rendered {len(frames)} frames to: {config.result_dir}")


def main():
    """Main rendering function."""
    config = tyro.cli(OptimizedRenderConfig)
    
    print("Starting optimized keyframes 3DGS rendering...")
    print(f"Config: {config}")
    
    renderer = OptimizedKeyframesRenderer(config)
    renderer.render_trajectory()
    
    print("Rendering completed!")


if __name__ == "__main__":
    main()
