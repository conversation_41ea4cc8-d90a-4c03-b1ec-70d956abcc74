#!/usr/bin/env python3
"""
修复相机位姿问题的脚本
"""

import torch
import numpy as np
import imageio
from keyframes_dataset import KeyframesDataset, KeyframesParser
from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
from gsplat.rendering import rasterization


def fix_camera_pose():
    """修复相机位姿问题"""
    print("=== 修复相机位姿问题 ===")
    
    # 1. 加载模型
    ckpt = torch.load("results/keyframes_fixed/ckpts/ckpt_4999.pt", map_location="cuda", weights_only=False)
    
    dataset = KeyframesDataset(
        data_path="data/keyframes/24.pkl",
        confidence_threshold=0.0,
        device="cuda"
    )
    parser = KeyframesParser(dataset)
    
    splats, _ = create_splats_with_optimizers_keyframes(
        parser,
        init_opacity=0.5,
        init_scale=10.0,
        device="cuda"
    )
    splats.load_state_dict(ckpt["splats"])
    
    # 2. 获取原始数据
    data_item = dataset[0]
    original_camtoworld = data_item['camtoworld'][0]  # [4, 4]
    K = data_item['K'][0]  # [3, 3]
    target_image = data_item['image'][0]  # [3, H, W]
    
    means = splats["means"]
    quats = splats["quats"]
    scales = torch.exp(splats["scales"])
    opacities = torch.sigmoid(splats["opacities"])
    colors = torch.cat([splats["sh0"], splats["shN"]], 1)
    
    print(f"原始相机位置: {original_camtoworld[:3, 3]}")
    print(f"点云中心: {means.mean(dim=0)}")
    print(f"点云范围: X[{means[:, 0].min():.3f}, {means[:, 0].max():.3f}]")
    print(f"点云范围: Y[{means[:, 1].min():.3f}, {means[:, 1].max():.3f}]")
    print(f"点云范围: Z[{means[:, 2].min():.3f}, {means[:, 2].max():.3f}]")
    
    # 3. 尝试不同的相机位姿修复方法
    
    # 方法1: 翻转Z轴（常见的坐标系问题）
    print("\n--- 方法1: 翻转Z轴 ---")
    flip_z_camtoworld = original_camtoworld.clone()
    flip_z_camtoworld[2, 3] *= -1  # 翻转Z位置
    flip_z_camtoworld[:3, 2] *= -1  # 翻转Z轴方向
    
    success1 = test_rendering(means, quats, scales, opacities, colors, flip_z_camtoworld, K, "方法1_翻转Z")
    
    # 方法2: 相机后退
    print("\n--- 方法2: 相机后退 ---")
    backward_camtoworld = original_camtoworld.clone()
    # 计算点云中心
    scene_center = means.mean(dim=0)
    # 将相机放在点云前方
    camera_distance = 3.0  # 距离点云中心3个单位
    backward_camtoworld[:3, 3] = scene_center + torch.tensor([0, 0, camera_distance], device=scene_center.device)
    
    success2 = test_rendering(means, quats, scales, opacities, colors, backward_camtoworld, K, "方法2_后退")
    
    # 方法3: 重新定位相机（看向点云中心）
    print("\n--- 方法3: 重新定位相机 ---")
    scene_center = means.mean(dim=0)
    
    # 相机位置：在点云前方
    camera_pos = scene_center + torch.tensor([0, 0, 2.0], device=scene_center.device)
    
    # 构建look-at矩阵
    forward = (scene_center - camera_pos)
    forward = forward / torch.norm(forward)
    
    up = torch.tensor([0, 1, 0], device=scene_center.device, dtype=torch.float32)
    right = torch.cross(forward, up)
    right = right / torch.norm(right)
    up = torch.cross(right, forward)
    
    lookat_camtoworld = torch.eye(4, device=scene_center.device)
    lookat_camtoworld[:3, 0] = right
    lookat_camtoworld[:3, 1] = up
    lookat_camtoworld[:3, 2] = -forward  # 注意OpenGL约定
    lookat_camtoworld[:3, 3] = camera_pos
    
    success3 = test_rendering(means, quats, scales, opacities, colors, lookat_camtoworld, K, "方法3_lookat")
    
    # 方法4: 使用原始相机但移动点云
    print("\n--- 方法4: 移动点云到相机前方 ---")
    # 将点云移动到相机前方
    camera_pos = original_camtoworld[:3, 3]
    camera_forward = original_camtoworld[:3, 2]  # Z轴方向
    
    # 将点云移动到相机前方2个单位
    moved_means = means - camera_pos + camera_pos - 2.0 * camera_forward
    
    success4 = test_rendering(moved_means, quats, scales, opacities, colors, original_camtoworld, K, "方法4_移动点云")
    
    # 总结
    print(f"\n=== 修复结果总结 ===")
    print(f"方法1 (翻转Z轴): {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"方法2 (相机后退): {'✅ 成功' if success2 else '❌ 失败'}")
    print(f"方法3 (重新定位): {'✅ 成功' if success3 else '❌ 失败'}")
    print(f"方法4 (移动点云): {'✅ 成功' if success4 else '❌ 失败'}")
    
    # 保存目标图像用于对比
    target_img = target_image.permute(1, 2, 0).detach().cpu().numpy()
    target_img_uint8 = (np.clip(target_img, 0, 1) * 255).astype(np.uint8)
    imageio.imwrite("target_image.png", target_img_uint8)
    print("✅ 保存目标图像到 target_image.png")


def test_rendering(means, quats, scales, opacities, colors, camtoworld, K, method_name):
    """测试渲染并保存结果"""
    try:
        # 检查深度
        viewmat = torch.linalg.inv(camtoworld.unsqueeze(0))
        means_hom = torch.cat([means, torch.ones(len(means), 1, device=means.device)], dim=1)
        means_cam = (viewmat[0] @ means_hom.T).T[:, :3]
        depths = means_cam[:, 2]
        
        positive_depth_count = (depths > 0).sum().item()
        print(f"  正深度点数量: {positive_depth_count}/{len(depths)}")
        print(f"  深度范围: [{depths.min():.3f}, {depths.max():.3f}]")
        
        if positive_depth_count == 0:
            print(f"  ❌ {method_name}: 所有点仍在相机后面")
            return False
        
        # 执行渲染
        render_colors, render_alphas, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=viewmat,
            Ks=K.unsqueeze(0),
            width=512,
            height=288,
            sh_degree=3,
        )
        
        visible_gaussians = (info['radii'] > 0).sum().item()
        max_color = render_colors.max().item()
        
        print(f"  可见Gaussian: {visible_gaussians}")
        print(f"  最大颜色值: {max_color:.6f}")
        
        if max_color > 0.01:  # 有明显的渲染内容
            # 保存渲染结果
            render_img = render_colors[0].detach().cpu().numpy()
            render_img_uint8 = (np.clip(render_img, 0, 1) * 255).astype(np.uint8)
            imageio.imwrite(f"fixed_render_{method_name}.png", render_img_uint8)
            print(f"  ✅ {method_name}: 渲染成功，保存到 fixed_render_{method_name}.png")
            return True
        else:
            print(f"  ❌ {method_name}: 渲染内容太暗")
            return False
            
    except Exception as e:
        print(f"  ❌ {method_name}: 渲染失败 - {e}")
        return False


def create_manual_camera():
    """手动创建一个合理的相机位姿"""
    print("\n=== 创建手动相机位姿 ===")
    
    # 加载点云数据
    dataset = KeyframesDataset(
        data_path="data/keyframes/24.pkl",
        confidence_threshold=0.0,
        device="cuda"
    )
    
    points = dataset.points_3d  # numpy array
    scene_center = points.mean(axis=0)
    scene_size = np.max(points.max(axis=0) - points.min(axis=0))
    
    print(f"场景中心: {scene_center}")
    print(f"场景尺寸: {scene_size:.3f}")
    
    # 创建一个合理的相机位姿
    camera_distance = scene_size * 1.5  # 距离场景1.5倍尺寸
    camera_pos = scene_center + np.array([0, 0, camera_distance])
    
    # 创建look-at矩阵
    forward = scene_center - camera_pos
    forward = forward / np.linalg.norm(forward)
    
    up = np.array([0, 1, 0])
    right = np.cross(forward, up)
    right = right / np.linalg.norm(right)
    up = np.cross(right, forward)
    
    manual_camtoworld = np.eye(4)
    manual_camtoworld[:3, 0] = right
    manual_camtoworld[:3, 1] = up
    manual_camtoworld[:3, 2] = -forward  # OpenGL约定
    manual_camtoworld[:3, 3] = camera_pos
    
    print(f"手动相机位置: {camera_pos}")
    print(f"手动相机位姿:\n{manual_camtoworld}")
    
    # 保存这个位姿供后续使用
    np.save("manual_camera_pose.npy", manual_camtoworld)
    print("✅ 保存手动相机位姿到 manual_camera_pose.npy")


def main():
    """主函数"""
    print("🔧 开始修复相机位姿问题...")
    
    fix_camera_pose()
    create_manual_camera()
    
    print("\n🎯 修复建议:")
    print("1. 检查生成的渲染图像")
    print("2. 如果某个方法成功，可以在训练时使用对应的相机位姿")
    print("3. 考虑重新训练时使用修正的相机位姿")


if __name__ == "__main__":
    main()
