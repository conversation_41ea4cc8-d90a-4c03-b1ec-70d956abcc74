#!/usr/bin/env python3
"""
分块渲染器 - 处理大量Gaussian的高质量渲染
"""

import os
import torch
import numpy as np
import imageio
import yaml
from typing import Dict, List, Tuple
from dataclasses import dataclass
import tyro

from keyframes_dataset import KeyframesDataset
from gsplat.rendering import rasterization


@dataclass
class ChunkedRenderConfig:
    """分块渲染配置"""
    
    # Model settings
    ckpt_path: str  # Path to checkpoint file
    result_dir: str = "results/chunked_render"  # Output directory
    
    # Chunked rendering settings
    chunk_size: int = 500000  # 每块的Gaussian数量
    overlap_ratio: float = 0.1  # 块之间的重叠比例
    blend_mode: str = "alpha"  # 混合模式: "alpha", "additive", "max"
    
    # Quality settings
    use_depth_sorting: bool = True  # 使用深度排序
    use_importance_sampling: bool = True  # 使用重要性采样
    
    # Camera settings
    width: int = 512
    height: int = 288
    
    # Output settings
    save_intermediate: bool = False  # 保存中间结果
    
    # Device
    device: str = "cuda"


class ChunkedRenderer:
    """分块渲染器"""
    
    def __init__(self, config: ChunkedRenderConfig):
        self.config = config
        self.device = config.device
        
        os.makedirs(config.result_dir, exist_ok=True)
        
        # 加载模型
        self.load_model()
        
        print(f"✅ 分块渲染器初始化完成")
        print(f"   - 总Gaussians: {self.num_gaussians:,}")
        print(f"   - 块大小: {config.chunk_size:,}")
        print(f"   - 预计块数: {self.num_chunks}")
    
    def load_model(self):
        """加载模型"""
        print(f"加载checkpoint: {self.config.ckpt_path}")
        
        ckpt = torch.load(self.config.ckpt_path, map_location=self.device, weights_only=False)
        
        # 获取训练配置
        cfg_path = os.path.join(os.path.dirname(self.config.ckpt_path), "..", "cfg.yml")
        if os.path.exists(cfg_path):
            with open(cfg_path, 'r') as f:
                self.training_cfg = yaml.safe_load(f)
        else:
            self.training_cfg = {}
        
        # 获取训练参数
        ckpt_filename = os.path.basename(self.config.ckpt_path)
        if "ckpt_" in ckpt_filename:
            training_step = int(ckpt_filename.split("ckpt_")[1].split(".")[0])
        else:
            training_step = 30000
        
        sh_degree_interval = self.training_cfg.get('sh_degree_interval', 1000)
        max_sh_degree = self.training_cfg.get('sh_degree', 3)
        self.sh_degree = min(training_step // sh_degree_interval, max_sh_degree)
        
        self.antialiased = self.training_cfg.get('antialiased', False)
        self.rasterize_mode = "antialiased" if self.antialiased else "classic"
        self.packed = self.training_cfg.get('packed', False)
        self.near_plane = self.training_cfg.get('near_plane', 0.01)
        self.far_plane = self.training_cfg.get('far_plane', 1e10)
        
        # 加载splats
        self.splats = ckpt["splats"]
        self.num_gaussians = self.splats["means"].shape[0]
        
        # 计算块数
        self.num_chunks = (self.num_gaussians + self.config.chunk_size - 1) // self.config.chunk_size
        
        # 移动到GPU
        for key in self.splats:
            if isinstance(self.splats[key], torch.Tensor):
                self.splats[key] = self.splats[key].to(self.device)
        
        # 加载数据集
        data_path = self.training_cfg.get('data_path', 'data/keyframes')
        if os.path.isdir(data_path):
            pkl_files = [f for f in os.listdir(data_path) if f.endswith('.pkl')]
            if pkl_files:
                data_path = os.path.join(data_path, sorted(pkl_files)[0])
        
        self.dataset = KeyframesDataset(
            data_path=data_path,
            confidence_threshold=0.0,
            device=self.device
        )
    
    def sort_gaussians_by_depth(self, camtoworld: torch.Tensor) -> torch.Tensor:
        """按深度排序Gaussian"""
        if not self.config.use_depth_sorting:
            return torch.arange(self.num_gaussians, device=self.device)
        
        # 计算相机坐标系下的深度
        means = self.splats["means"]
        camera_pos = camtoworld[:3, 3]
        
        # 计算到相机的距离
        distances = torch.norm(means - camera_pos, dim=1)
        
        # 按距离排序（从近到远）
        _, indices = torch.sort(distances)
        
        return indices
    
    def sample_important_gaussians(self, indices: torch.Tensor, chunk_idx: int) -> torch.Tensor:
        """重要性采样Gaussian"""
        if not self.config.use_importance_sampling:
            # 简单的顺序分块
            start_idx = chunk_idx * self.config.chunk_size
            end_idx = min(start_idx + self.config.chunk_size, len(indices))
            return indices[start_idx:end_idx]
        
        # 计算重要性权重
        opacities = torch.sigmoid(self.splats["opacities"])
        scales = torch.exp(self.splats["scales"])
        avg_scale = scales.mean(dim=1)
        
        # 重要性 = 透明度 / 平均尺度
        importance = opacities / (avg_scale + 1e-8)
        
        # 对当前块进行重要性采样
        start_idx = chunk_idx * self.config.chunk_size
        end_idx = min(start_idx + self.config.chunk_size, len(indices))
        chunk_indices = indices[start_idx:end_idx]
        
        if len(chunk_indices) <= self.config.chunk_size:
            return chunk_indices
        
        # 基于重要性采样
        chunk_importance = importance[chunk_indices]
        probs = chunk_importance / chunk_importance.sum()
        
        sampled_indices = torch.multinomial(probs, self.config.chunk_size, replacement=False)
        return chunk_indices[sampled_indices]
    
    def render_chunk(
        self, 
        chunk_indices: torch.Tensor,
        camtoworld: torch.Tensor,
        K: torch.Tensor,
        width: int,
        height: int
    ) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """渲染单个块"""
        
        # 提取块数据
        chunk_splats = {}
        for key, value in self.splats.items():
            if isinstance(value, torch.Tensor) and len(value) == self.num_gaussians:
                chunk_splats[key] = value[chunk_indices]
            else:
                chunk_splats[key] = value
        
        # 准备渲染参数
        means = chunk_splats["means"]
        quats = chunk_splats["quats"]
        scales = torch.exp(chunk_splats["scales"])
        opacities = torch.sigmoid(chunk_splats["opacities"])
        colors = torch.cat([chunk_splats["sh0"], chunk_splats["shN"]], 1)
        
        # 渲染
        render_colors, render_alphas, info = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=width,
            height=height,
            sh_degree=self.sh_degree,
            packed=self.packed,
            rasterize_mode=self.rasterize_mode,
            near_plane=self.near_plane,
            far_plane=self.far_plane,
        )
        
        return render_colors[0], render_alphas[0], info
    
    def blend_chunks(self, chunk_results: List[Tuple[torch.Tensor, torch.Tensor]]) -> torch.Tensor:
        """混合多个块的渲染结果"""
        if len(chunk_results) == 1:
            return chunk_results[0][0]
        
        if self.config.blend_mode == "alpha":
            # Alpha混合
            final_color = torch.zeros_like(chunk_results[0][0])
            final_alpha = torch.zeros_like(chunk_results[0][1])
            
            for color, alpha in chunk_results:
                # 标准alpha混合
                final_color = final_color * (1 - alpha) + color * alpha
                final_alpha = final_alpha + alpha * (1 - final_alpha)
            
            return final_color
        
        elif self.config.blend_mode == "additive":
            # 加法混合
            final_color = torch.zeros_like(chunk_results[0][0])
            for color, alpha in chunk_results:
                final_color += color * alpha
            return torch.clamp(final_color, 0, 1)
        
        elif self.config.blend_mode == "max":
            # 最大值混合
            final_color = torch.zeros_like(chunk_results[0][0])
            for color, alpha in chunk_results:
                weighted_color = color * alpha
                final_color = torch.maximum(final_color, weighted_color)
            return final_color
        
        else:
            raise ValueError(f"Unknown blend mode: {self.config.blend_mode}")
    
    def render_view(
        self,
        camtoworld: torch.Tensor,
        K: torch.Tensor,
        width: int,
        height: int,
    ) -> torch.Tensor:
        """渲染单个视角（分块）"""
        
        print(f"分块渲染: {self.num_chunks} 块")
        
        # 按深度排序
        sorted_indices = self.sort_gaussians_by_depth(camtoworld)
        
        # 渲染每个块
        chunk_results = []
        for chunk_idx in range(self.num_chunks):
            # 采样当前块的Gaussian
            chunk_indices = self.sample_important_gaussians(sorted_indices, chunk_idx)
            
            if len(chunk_indices) == 0:
                continue
            
            # 渲染块
            color, alpha, info = self.render_chunk(
                chunk_indices, camtoworld, K, width, height
            )
            
            chunk_results.append((color, alpha))
            
            # 保存中间结果
            if self.config.save_intermediate:
                chunk_img = torch.clamp(color, 0, 1).cpu().numpy()
                chunk_img = (chunk_img * 255).astype(np.uint8)
                chunk_path = os.path.join(self.config.result_dir, f"chunk_{chunk_idx:03d}.png")
                imageio.imwrite(chunk_path, chunk_img)
            
            visible_gaussians = (info['radii'] > 0).sum().item()
            print(f"  块 {chunk_idx+1}/{self.num_chunks}: "
                  f"{len(chunk_indices):,} Gaussians, "
                  f"{visible_gaussians:,} 可见")
        
        # 混合所有块
        print("混合块结果...")
        final_image = self.blend_chunks(chunk_results)
        
        return final_image
    
    def render_keyframe(self, frame_idx: int = 0) -> str:
        """渲染单个关键帧"""
        data_item = self.dataset[frame_idx]
        pose = data_item['camtoworld'][0]
        K = data_item['K'][0]
        
        print(f"渲染关键帧 {frame_idx}...")
        
        with torch.no_grad():
            rendered = self.render_view(
                camtoworld=pose,
                K=K,
                width=self.config.width,
                height=self.config.height,
            )
            
            # 保存结果
            frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
            frame = (frame * 255).astype(np.uint8)
            
            output_path = os.path.join(self.config.result_dir, f"keyframe_{frame_idx:04d}_chunked.png")
            imageio.imwrite(output_path, frame)
            
            print(f"✅ 渲染完成: {output_path}")
            print(f"   颜色范围: [{rendered.min():.3f}, {rendered.max():.3f}]")
            
            return output_path


def main():
    """主函数"""
    config = tyro.cli(ChunkedRenderConfig)
    
    print("=== 分块渲染器 ===")
    print(f"配置: {config}")
    
    renderer = ChunkedRenderer(config)
    
    # 渲染第一个关键帧
    output_path = renderer.render_keyframe(0)
    
    print(f"\n✅ 分块渲染完成!")
    print(f"输出: {output_path}")


if __name__ == "__main__":
    main()
