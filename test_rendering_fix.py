#!/usr/bin/env python3
"""
测试渲染修复效果的脚本
"""

import os
import torch
import imageio
import numpy as np
from keyframes_renderer_fixed import FixedKeyframesRenderer, FixedRenderConfig


def test_rendering_fix():
    """测试渲染修复效果"""
    print("=== 测试渲染修复效果 ===")
    
    # 配置
    ckpt_path = "results/multi_keyframes/ckpts/ckpt_29999.pt"
    optimized_ckpt_path = "results/multi_keyframes/ckpts/ckpt_29999_optimized.pt"
    
    # 检查文件是否存在
    if not os.path.exists(ckpt_path):
        print(f"❌ 原始checkpoint不存在: {ckpt_path}")
        return
    
    print(f"✅ 找到原始checkpoint: {ckpt_path}")
    
    # 测试1: 原始模型渲染关键帧
    print("\n--- 测试1: 原始模型渲染关键帧 ---")
    config1 = FixedRenderConfig(
        ckpt_path=ckpt_path,
        render_mode="keyframes",
        result_dir="results/test_original",
        save_video=False
    )
    
    try:
        renderer1 = FixedKeyframesRenderer(config1)
        renderer1.render_keyframes_comparison()
        print("✅ 原始模型渲染成功")
    except Exception as e:
        print(f"❌ 原始模型渲染失败: {e}")
    
    # 测试2: 优化模型渲染（如果存在）
    if os.path.exists(optimized_ckpt_path):
        print("\n--- 测试2: 优化模型渲染关键帧 ---")
        config2 = FixedRenderConfig(
            ckpt_path=optimized_ckpt_path,
            render_mode="keyframes",
            result_dir="results/test_optimized",
            save_video=False
        )
        
        try:
            renderer2 = FixedKeyframesRenderer(config2)
            renderer2.render_keyframes_comparison()
            print("✅ 优化模型渲染成功")
        except Exception as e:
            print(f"❌ 优化模型渲染失败: {e}")
        
        # 测试3: 新视角渲染（少量帧）
        print("\n--- 测试3: 新视角渲染 ---")
        config3 = FixedRenderConfig(
            ckpt_path=optimized_ckpt_path,
            render_mode="turntable",
            num_frames=5,  # 只渲染5帧测试
            result_dir="results/test_novel_view",
            save_video=False  # 不保存视频，避免编码问题
        )
        
        try:
            renderer3 = FixedKeyframesRenderer(config3)
            renderer3.render_trajectory()
            print("✅ 新视角渲染成功")
        except Exception as e:
            print(f"❌ 新视角渲染失败: {e}")
    else:
        print(f"\n⚠️  优化模型不存在: {optimized_ckpt_path}")
        print("   可以运行以下命令创建优化模型:")
        print(f"   python optimize_gaussians.py --ckpt_path {ckpt_path}")
    
    # 检查输出结果
    print("\n=== 检查输出结果 ===")
    
    test_dirs = ["results/test_original", "results/test_optimized", "results/test_novel_view"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            files = [f for f in os.listdir(test_dir) if f.endswith('.png')]
            print(f"📁 {test_dir}: {len(files)} 个图像文件")
            
            # 显示第一个渲染图像的统计信息
            if files:
                first_file = os.path.join(test_dir, sorted(files)[0])
                try:
                    img = imageio.imread(first_file)
                    print(f"   - {files[0]}: 尺寸 {img.shape}, 范围 [{img.min()}, {img.max()}]")
                except Exception as e:
                    print(f"   - 无法读取 {files[0]}: {e}")
        else:
            print(f"📁 {test_dir}: 不存在")
    
    print("\n=== 测试完成 ===")
    print("如果看到渲染成功的消息，说明修复方案有效！")
    print("您可以查看生成的图像来评估渲染质量。")


def compare_rendering_quality():
    """比较渲染质量"""
    print("\n=== 渲染质量对比 ===")
    
    # 检查是否有对比图像
    original_dir = "results/test_original"
    optimized_dir = "results/test_optimized"
    
    if not os.path.exists(original_dir) or not os.path.exists(optimized_dir):
        print("⚠️  需要先运行渲染测试")
        return
    
    # 查找对应的渲染图像
    original_files = [f for f in os.listdir(original_dir) if f.startswith('rendered_') and f.endswith('.png')]
    optimized_files = [f for f in os.listdir(optimized_dir) if f.startswith('rendered_') and f.endswith('.png')]
    
    if not original_files or not optimized_files:
        print("⚠️  未找到渲染图像")
        return
    
    # 比较第一张图像
    original_img = imageio.imread(os.path.join(original_dir, original_files[0]))
    optimized_img = imageio.imread(os.path.join(optimized_dir, optimized_files[0]))
    
    print(f"原始模型渲染:")
    print(f"  - 尺寸: {original_img.shape}")
    print(f"  - 像素范围: [{original_img.min()}, {original_img.max()}]")
    print(f"  - 平均亮度: {original_img.mean():.2f}")
    print(f"  - 非零像素比例: {(original_img > 0).mean():.3f}")
    
    print(f"优化模型渲染:")
    print(f"  - 尺寸: {optimized_img.shape}")
    print(f"  - 像素范围: [{optimized_img.min()}, {optimized_img.max()}]")
    print(f"  - 平均亮度: {optimized_img.mean():.2f}")
    print(f"  - 非零像素比例: {(optimized_img > 0).mean():.3f}")
    
    # 计算差异
    if original_img.shape == optimized_img.shape:
        diff = np.abs(original_img.astype(float) - optimized_img.astype(float))
        print(f"图像差异:")
        print(f"  - 平均差异: {diff.mean():.2f}")
        print(f"  - 最大差异: {diff.max():.2f}")
        print(f"  - 差异标准差: {diff.std():.2f}")


def main():
    """主函数"""
    test_rendering_fix()
    compare_rendering_quality()
    
    print("\n🎉 测试脚本完成！")
    print("\n📋 总结:")
    print("1. 如果渲染成功，说明修复方案有效")
    print("2. 查看生成的图像评估视觉质量")
    print("3. 优化模型应该比原始模型渲染更快、质量更好")
    print("4. 新视角渲染应该没有大面积黑色区域")


if __name__ == "__main__":
    main()
