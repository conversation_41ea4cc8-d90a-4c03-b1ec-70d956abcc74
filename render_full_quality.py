#!/usr/bin/env python3
"""
Full quality rendering using all Gaussians (like training evaluation).
"""

import os
import numpy as np
import torch
import imageio
from typing import Tuple
import pickle

from gsplat.rendering import rasterization


def load_keyframe_camera(pkl_path: str) -> Tuple[torch.Tensor, torch.Tensor, np.ndarray]:
    """Load camera parameters from keyframe file."""
    with open(pkl_path, 'rb') as f:
        data = pickle.load(f)
    
    # Extract camera pose and image
    T_WC = data['T_WC']  # [1, 4, 4] or [4, 4]
    if T_WC.shape[0] == 1:
        T_WC = T_WC[0]  # [4, 4]
    
    # Convert to tensor
    if isinstance(T_WC, torch.Tensor):
        camtoworld = T_WC.float()
    else:
        camtoworld = torch.from_numpy(T_WC).float()
    
    # Get image
    img = data['img']  # [1, 3, H, W]
    if img.shape[0] == 1:
        img = img[0]  # [3, H, W]
    
    # Convert to [H, W, 3] and ensure [0, 1] range
    if img.min() < 0:
        img = (img + 1.0) / 2.0
    img = img.transpose(1, 2, 0)  # [H, W, 3]
    
    # Estimate camera intrinsics
    H, W = img.shape[:2]
    fov_deg = 60.0
    fov_rad = np.deg2rad(fov_deg)
    fx = fy = W / (2.0 * np.tan(fov_rad / 2.0))
    cx = W / 2.0
    cy = H / 2.0
    
    K = torch.tensor([
        [fx, 0.0, cx],
        [0.0, fy, cy],
        [0.0, 0.0, 1.0]
    ], dtype=torch.float32)
    
    return camtoworld, K, img


def render_full_quality(ckpt_path: str, camtoworld: torch.Tensor, K: torch.Tensor, 
                       width: int, height: int, device: str = "cuda") -> np.ndarray:
    """Render using all Gaussians with full quality (like training evaluation)."""
    
    # Load checkpoint
    print(f"Loading checkpoint from: {ckpt_path}")
    ckpt = torch.load(ckpt_path, map_location=device, weights_only=False)
    
    # Load all splats to GPU
    print("Loading all Gaussians to GPU...")
    means = ckpt["splats"]["means"].to(device)
    quats = ckpt["splats"]["quats"].to(device)
    scales = torch.exp(ckpt["splats"]["scales"].to(device))
    opacities = torch.sigmoid(ckpt["splats"]["opacities"].to(device))
    colors = torch.cat([
        ckpt["splats"]["sh0"].to(device),
        ckpt["splats"]["shN"].to(device)
    ], 1)
    
    print(f"Rendering with {len(means)} Gaussians...")
    
    # Render with exact same parameters as training
    with torch.no_grad():
        render_colors, _, _ = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0).to(device)),
            Ks=K.unsqueeze(0).to(device),
            width=width,
            height=height,
            sh_degree=3,  # Full SH degree
            packed=False,  # Same as training
            rasterize_mode="antialiased",  # Same as training
            near_plane=0.01,  # Same as training default
            far_plane=1e10,   # Same as training default
        )
    
    # Convert to numpy
    rendered = torch.clamp(render_colors[0], 0.0, 1.0).cpu().numpy()
    
    # Clear GPU memory
    del means, quats, scales, opacities, colors, render_colors
    torch.cuda.empty_cache()
    
    return rendered


def generate_turntable_poses(num_frames: int, radius: float = 2.0, height: float = 0.0, device: str = "cuda"):
    """Generate turntable camera poses."""
    poses = []

    for i in range(num_frames):
        angle = 2 * np.pi * i / num_frames

        # Camera position
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        z = height
        cam_pos = torch.tensor([x, y, z], device=device, dtype=torch.float32)

        # Look at origin
        forward = -cam_pos / torch.norm(cam_pos)
        up = torch.tensor([0.0, 0.0, 1.0], device=device, dtype=torch.float32)
        right = torch.cross(forward, up)
        right = right / torch.norm(right)
        up = torch.cross(right, forward)

        # Create rotation matrix
        R = torch.stack([right, up, -forward], dim=1)

        # Create pose matrix
        pose = torch.eye(4, device=device, dtype=torch.float32)
        pose[:3, :3] = R
        pose[:3, 3] = cam_pos

        poses.append(pose)

    return poses


def main():
    """Main function."""
    # Configuration
    ckpt_path = "results/multi_keyframes/ckpts/ckpt_29999.pt"
    keyframe_path = "data/keyframes/24.pkl"
    output_dir = "results/full_quality_comparison"

    os.makedirs(output_dir, exist_ok=True)

    print("Loading keyframe camera parameters...")
    camtoworld, K, original_img = load_keyframe_camera(keyframe_path)

    print(f"Original image shape: {original_img.shape}")
    print(f"Camera pose shape: {camtoworld.shape}")

    try:
        print("Rendering with full quality (all Gaussians)...")
        rendered_full = render_full_quality(
            ckpt_path, camtoworld, K,
            original_img.shape[1], original_img.shape[0]
        )

        print(f"Full quality rendered image shape: {rendered_full.shape}")

        # Save results
        original_path = os.path.join(output_dir, "original_keyframe.png")
        rendered_path = os.path.join(output_dir, "rendered_full_quality.png")
        comparison_path = os.path.join(output_dir, "comparison_full_quality.png")

        imageio.imwrite(original_path, (original_img * 255).astype(np.uint8))
        imageio.imwrite(rendered_path, (rendered_full * 255).astype(np.uint8))

        # Create side-by-side comparison
        comparison = np.concatenate([original_img, rendered_full], axis=1)
        imageio.imwrite(comparison_path, (comparison * 255).astype(np.uint8))

        print(f"Results saved to:")
        print(f"  Original: {original_path}")
        print(f"  Full quality rendered: {rendered_path}")
        print(f"  Comparison: {comparison_path}")

        # Calculate quality metrics
        def calculate_metrics(img1, img2):
            mse = np.mean((img1 - img2) ** 2)
            psnr = 20 * np.log10(1.0 / np.sqrt(mse)) if mse > 0 else float('inf')
            return mse, psnr

        mse, psnr = calculate_metrics(original_img, rendered_full)
        print(f"\\nQuality metrics:")
        print(f"  MSE: {mse:.6f}")
        print(f"  PSNR: {psnr:.2f} dB")

        # Generate a few high-quality novel views
        print("\\nGenerating high-quality novel views...")
        novel_dir = os.path.join(output_dir, "novel_views")
        os.makedirs(novel_dir, exist_ok=True)

        poses = generate_turntable_poses(8, radius=2.0, height=0.0)  # Only 8 frames to save memory

        for i, pose in enumerate(poses):
            print(f"Rendering novel view {i+1}/8...")
            rendered_novel = render_full_quality(
                ckpt_path, pose.cpu(), K,
                original_img.shape[1], original_img.shape[0]
            )

            novel_path = os.path.join(novel_dir, f"novel_view_{i:02d}.png")
            imageio.imwrite(novel_path, (rendered_novel * 255).astype(np.uint8))

        print(f"Novel views saved to: {novel_dir}")

    except RuntimeError as e:
        if "out of memory" in str(e):
            print("GPU out of memory! The model is too large for full quality rendering.")
            print("This explains why the optimized version (with fewer Gaussians) has lower quality.")
            print("The training evaluation uses the same GPU memory management as training.")
        else:
            raise e


if __name__ == "__main__":
    main()
