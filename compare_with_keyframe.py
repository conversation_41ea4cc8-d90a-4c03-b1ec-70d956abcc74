#!/usr/bin/env python3
"""
Compare rendered views with original keyframes.
"""

import os
import numpy as np
import torch
import imageio
from typing import Tuple
import pickle

from keyframes_renderer_optimized import OptimizedKeyframesRenderer, OptimizedRenderConfig
from keyframes_dataset import KeyframesDataset


def load_keyframe_camera(pkl_path: str) -> Tuple[torch.Tensor, torch.Tensor, np.ndarray]:
    """Load camera parameters from keyframe file."""
    with open(pkl_path, 'rb') as f:
        data = pickle.load(f)
    
    # Extract camera pose and image
    T_WC = data['T_WC']  # [1, 4, 4] or [4, 4]
    if T_WC.shape[0] == 1:
        T_WC = T_WC[0]  # [4, 4]
    
    # Convert to tensor
    if isinstance(T_WC, torch.Tensor):
        camtoworld = T_WC.float()
    else:
        camtoworld = torch.from_numpy(T_WC).float()
    
    # Get image
    img = data['img']  # [1, 3, H, W]
    if img.shape[0] == 1:
        img = img[0]  # [3, H, W]
    
    # Convert to [H, W, 3] and ensure [0, 1] range
    if img.min() < 0:
        img = (img + 1.0) / 2.0
    img = img.transpose(1, 2, 0)  # [H, W, 3]
    
    # Estimate camera intrinsics
    H, W = img.shape[:2]
    fov_deg = 60.0
    fov_rad = np.deg2rad(fov_deg)
    fx = fy = W / (2.0 * np.tan(fov_rad / 2.0))
    cx = W / 2.0
    cy = H / 2.0
    
    K = torch.tensor([
        [fx, 0.0, cx],
        [0.0, fy, cy],
        [0.0, 0.0, 1.0]
    ], dtype=torch.float32)
    
    return camtoworld, K, img


def render_at_keyframe_pose(renderer: OptimizedKeyframesRenderer, 
                           camtoworld: torch.Tensor, 
                           K: torch.Tensor,
                           width: int, 
                           height: int) -> np.ndarray:
    """Render at a specific keyframe pose."""
    with torch.no_grad():
        rendered = renderer.rasterize_splats_chunked(
            camtoworld=camtoworld.to(renderer.device),
            K=K.to(renderer.device),
            width=width,
            height=height,
        )
        
        # Convert to numpy
        frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
        return frame


def create_comparison(original: np.ndarray, rendered: np.ndarray, output_path: str):
    """Create side-by-side comparison."""
    # Ensure both images have the same height
    h1, w1 = original.shape[:2]
    h2, w2 = rendered.shape[:2]
    
    if h1 != h2:
        # Resize to match heights
        from PIL import Image
        if h1 > h2:
            rendered_pil = Image.fromarray((rendered * 255).astype(np.uint8))
            rendered_pil = rendered_pil.resize((int(w2 * h1 / h2), h1), Image.LANCZOS)
            rendered = np.array(rendered_pil) / 255.0
        else:
            original_pil = Image.fromarray((original * 255).astype(np.uint8))
            original_pil = original_pil.resize((int(w1 * h2 / h1), h2), Image.LANCZOS)
            original = np.array(original_pil) / 255.0
    
    # Create side-by-side comparison
    comparison = np.concatenate([original, rendered], axis=1)
    
    # Save comparison
    comparison_uint8 = (comparison * 255).astype(np.uint8)
    imageio.imwrite(output_path, comparison_uint8)
    
    return comparison


def main():
    """Main comparison function."""
    # Configuration
    ckpt_path = "results/multi_keyframes/ckpts/ckpt_29999.pt"
    keyframe_path = "data/keyframes/24.pkl"
    output_dir = "results/keyframe_comparison"
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("Loading keyframe camera parameters...")
    camtoworld, K, original_img = load_keyframe_camera(keyframe_path)
    
    print(f"Original image shape: {original_img.shape}")
    print(f"Camera pose shape: {camtoworld.shape}")
    print(f"Camera intrinsics shape: {K.shape}")
    
    # Create renderer
    config = OptimizedRenderConfig(
        ckpt_path=ckpt_path,
        result_dir=output_dir,
        width=original_img.shape[1],  # Use original image dimensions
        height=original_img.shape[0],
        low_memory=True
    )
    
    print("Loading trained model...")
    renderer = OptimizedKeyframesRenderer(config)
    
    print("Rendering at keyframe pose...")
    rendered_img = render_at_keyframe_pose(
        renderer, camtoworld, K, 
        config.width, config.height
    )
    
    print(f"Rendered image shape: {rendered_img.shape}")
    
    # Save individual images
    original_path = os.path.join(output_dir, "original_keyframe.png")
    rendered_path = os.path.join(output_dir, "rendered_at_keyframe.png")
    comparison_path = os.path.join(output_dir, "comparison.png")
    
    imageio.imwrite(original_path, (original_img * 255).astype(np.uint8))
    imageio.imwrite(rendered_path, (rendered_img * 255).astype(np.uint8))
    
    # Create comparison
    print("Creating comparison...")
    create_comparison(original_img, rendered_img, comparison_path)
    
    print(f"Results saved to:")
    print(f"  Original: {original_path}")
    print(f"  Rendered: {rendered_path}")
    print(f"  Comparison: {comparison_path}")
    
    # Also render nearby poses for context
    print("Rendering nearby poses...")
    nearby_poses = []
    
    # Generate small perturbations around the keyframe pose
    for i, (dx, dy, dz) in enumerate([
        (0.1, 0, 0), (-0.1, 0, 0),  # Left/right
        (0, 0.1, 0), (0, -0.1, 0),  # Forward/back
        (0, 0, 0.1), (0, 0, -0.1),  # Up/down
    ]):
        perturbed_pose = camtoworld.clone()
        perturbed_pose[:3, 3] += torch.tensor([dx, dy, dz], device=perturbed_pose.device)
        
        rendered_nearby = render_at_keyframe_pose(
            renderer, perturbed_pose, K,
            config.width, config.height
        )
        
        nearby_path = os.path.join(output_dir, f"nearby_pose_{i:02d}.png")
        imageio.imwrite(nearby_path, (rendered_nearby * 255).astype(np.uint8))
        nearby_poses.append(rendered_nearby)
    
    print(f"Nearby poses saved to {output_dir}/nearby_pose_*.png")
    print("Comparison completed!")


if __name__ == "__main__":
    main()
