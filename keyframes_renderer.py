import os
import numpy as np
import torch
import imageio
import tqdm
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
from dataclasses import dataclass
import tyro

from keyframes_dataset import KeyframesDataset, KeyframesParser
from keyframes_trainer_utils import create_splats_with_optimizers_keyframes
from keyframes_trainer import KeyframesTrainer, KeyframesConfig
from gsplat.rendering import rasterization


@dataclass
class RenderConfig:
    """Configuration for rendering."""
    
    # Model settings
    ckpt_path: str  # Path to checkpoint file
    result_dir: str = "results/render"  # Output directory
    
    # Rendering settings
    render_mode: str = "novel_view"  # "novel_view", "trajectory", "turntable"
    num_frames: int = 120  # Number of frames for trajectory/turntable
    radius: float = 2.0  # Radius for turntable rendering
    height_offset: float = 0.0  # Height offset for camera
    
    # Camera settings
    width: int = 512
    height: int = 288
    fov: float = 60.0  # Field of view in degrees
    
    # Output settings
    save_video: bool = True
    fps: int = 30
    
    # Device
    device: str = "cuda"


class KeyframesRenderer:
    """Renderer for trained 3DGS models."""
    
    def __init__(self, config: RenderConfig):
        self.config = config
        self.device = config.device
        
        # Create output directory
        os.makedirs(config.result_dir, exist_ok=True)
        
        # Load checkpoint
        print(f"Loading checkpoint from: {config.ckpt_path}")
        ckpt = torch.load(config.ckpt_path, map_location=self.device, weights_only=False)
        
        # Extract original data path from checkpoint directory structure
        # Assume checkpoint is in results/keyframes/ckpts/ckpt_xxx.pt
        ckpt_dir = os.path.dirname(config.ckpt_path)
        result_dir = os.path.dirname(ckpt_dir)
        
        # Try to find the original config
        cfg_path = os.path.join(result_dir, "cfg.yml")
        if os.path.exists(cfg_path):
            try:
                import yaml
                with open(cfg_path, 'r') as f:
                    cfg_dict = yaml.safe_load(f)
                # Remove complex objects that can't be reconstructed
                if 'strategy' in cfg_dict:
                    del cfg_dict['strategy']
                # Reconstruct config with basic parameters
                self.train_cfg = KeyframesConfig(**cfg_dict)
            except Exception as e:
                print(f"Warning: Could not load config ({e}), using defaults")
                self.train_cfg = KeyframesConfig()
        else:
            # Use default config
            print("Warning: Could not find original config, using defaults")
            self.train_cfg = KeyframesConfig()
        
        # Load dataset (needed for scene information)
        # Check if this was multi-frame training
        if hasattr(self.train_cfg, 'multi_frame_mode') and self.train_cfg.multi_frame_mode:
            # For multi-frame, use the same dataset setup as training
            self.dataset = KeyframesDataset(
                data_path=self.train_cfg.data_path,
                split="train",
                confidence_threshold=self.train_cfg.confidence_threshold,
                max_points=self.train_cfg.max_points,
                normalize_points=self.train_cfg.normalize_points,
                device=self.device,
                multi_frame_mode=True,
                test_every=getattr(self.train_cfg, 'test_every', 8)
            )
        else:
            # Single frame mode
            self.dataset = KeyframesDataset(
                data_path=self.train_cfg.data_path,
                device=self.device
            )

        self.parser = KeyframesParser(self.dataset)

        # Recreate model with the same number of points as in checkpoint
        ckpt_means_shape = ckpt["splats"]["means"].shape
        num_points = ckpt_means_shape[0]

        print(f"Checkpoint contains {num_points} Gaussians")

        # Create a dummy parser with the correct number of points
        dummy_points = np.random.randn(num_points, 3).astype(np.float32)
        dummy_colors = np.random.randint(0, 255, (num_points, 3)).astype(np.uint8)

        # Temporarily replace parser points
        original_points = self.parser.points
        original_colors = self.parser.points_rgb
        self.parser.points = dummy_points
        self.parser.points_rgb = dummy_colors

        # Recreate model
        self.splats, _ = create_splats_with_optimizers_keyframes(
            self.parser,
            device=self.device,
        )

        # Restore original parser points for camera information
        self.parser.points = original_points
        self.parser.points_rgb = original_colors

        # Load trained parameters
        self.splats.load_state_dict(ckpt["splats"])
        self.scene_scale = ckpt.get("scene_scale", 1.0)
        
        print(f"Model loaded with {len(self.splats['means'])} Gaussians")
        print(f"Scene scale: {self.scene_scale}")
    
    def rasterize_splats(
        self,
        camtoworld: torch.Tensor,
        K: torch.Tensor,
        width: int,
        height: int,
        **kwargs,
    ) -> torch.Tensor:
        """Rasterize splats for a single camera."""
        means = self.splats["means"]
        quats = self.splats["quats"]
        scales = torch.exp(self.splats["scales"])
        opacities = torch.sigmoid(self.splats["opacities"])
        colors = torch.cat([self.splats["sh0"], self.splats["shN"]], 1)
        
        render_colors, _, _ = rasterization(
            means=means,
            quats=quats,
            scales=scales,
            opacities=opacities,
            colors=colors,
            viewmats=torch.linalg.inv(camtoworld.unsqueeze(0)),
            Ks=K.unsqueeze(0),
            width=width,
            height=height,
            **kwargs,
        )
        
        return render_colors[0]  # Remove batch dimension
    
    def generate_camera_trajectory(self, mode: str, num_frames: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """Generate camera trajectory."""
        # Get reference camera from dataset
        ref_data = self.dataset[0]
        ref_pose = ref_data["camtoworld"][0]  # [4, 4]
        ref_K = ref_data["K"][0]  # [3, 3]
        
        # Extract camera position and orientation
        ref_pos = ref_pose[:3, 3]
        ref_rot = ref_pose[:3, :3]
        
        poses = []
        
        if mode == "turntable":
            # Generate turntable trajectory around the scene center
            scene_center = torch.tensor([0.0, 0.0, 0.0], device=self.device)
            
            # Calculate initial radius and height
            initial_radius = torch.norm(ref_pos[:2] - scene_center[:2])
            if initial_radius < 0.1:
                initial_radius = self.config.radius
            
            height = ref_pos[2] + self.config.height_offset
            
            for i in range(num_frames):
                angle = 2 * np.pi * i / num_frames
                
                # Camera position
                x = scene_center[0] + initial_radius * np.cos(angle)
                y = scene_center[1] + initial_radius * np.sin(angle)
                z = height
                cam_pos = torch.tensor([x, y, z], device=self.device)
                
                # Look at scene center
                forward = scene_center - cam_pos
                forward = forward / torch.norm(forward)
                
                # Up vector
                up = torch.tensor([0.0, 0.0, 1.0], device=self.device)
                right = torch.cross(forward, up)
                right = right / torch.norm(right)
                up = torch.cross(right, forward)
                
                # Create rotation matrix
                R = torch.stack([right, up, -forward], dim=1)
                
                # Create pose matrix
                pose = torch.eye(4, device=self.device)
                pose[:3, :3] = R
                pose[:3, 3] = cam_pos
                
                poses.append(pose)
        
        elif mode == "trajectory":
            # Generate smooth trajectory between dataset cameras
            if len(self.dataset) > 1:
                # Interpolate between existing camera poses
                all_poses = []
                for i in range(len(self.dataset)):
                    data = self.dataset[i]
                    all_poses.append(data["camtoworld"][0])
                
                # Simple linear interpolation
                for i in range(num_frames):
                    t = i / (num_frames - 1) * (len(all_poses) - 1)
                    idx = int(t)
                    alpha = t - idx
                    
                    if idx >= len(all_poses) - 1:
                        pose = all_poses[-1]
                    else:
                        # Linear interpolation of positions
                        pos1 = all_poses[idx][:3, 3]
                        pos2 = all_poses[idx + 1][:3, 3]
                        pos = pos1 * (1 - alpha) + pos2 * alpha
                        
                        # SLERP for rotations (simplified)
                        rot1 = all_poses[idx][:3, :3]
                        rot2 = all_poses[idx + 1][:3, :3]
                        # Simple linear interpolation for now
                        rot = rot1 * (1 - alpha) + rot2 * alpha
                        
                        pose = torch.eye(4, device=self.device)
                        pose[:3, :3] = rot
                        pose[:3, 3] = pos
                    
                    poses.append(pose)
            else:
                # Fallback to turntable if only one camera
                return self.generate_camera_trajectory("turntable", num_frames)
        
        else:  # novel_view
            # Generate a few novel viewpoints around the reference camera
            for i in range(num_frames):
                # Small perturbations around reference pose
                angle_offset = (i / num_frames - 0.5) * 0.5  # ±0.25 radians
                height_offset = (i / num_frames - 0.5) * 0.2  # ±0.1 units
                
                # Rotate around Z axis
                cos_a, sin_a = np.cos(angle_offset), np.sin(angle_offset)
                rot_z = torch.tensor([
                    [cos_a, -sin_a, 0],
                    [sin_a, cos_a, 0],
                    [0, 0, 1]
                ], device=self.device, dtype=torch.float32)
                
                new_rot = ref_rot @ rot_z
                new_pos = ref_pos.clone()
                new_pos[2] += height_offset
                
                pose = torch.eye(4, device=self.device)
                pose[:3, :3] = new_rot
                pose[:3, 3] = new_pos
                
                poses.append(pose)
        
        return poses, ref_K
    
    def render_trajectory(self):
        """Render camera trajectory."""
        config = self.config
        
        print(f"Generating {config.render_mode} trajectory with {config.num_frames} frames...")
        poses, K = self.generate_camera_trajectory(config.render_mode, config.num_frames)
        
        # Render frames
        frames = []
        for i, pose in enumerate(tqdm.tqdm(poses, desc="Rendering")):
            with torch.no_grad():
                rendered = self.rasterize_splats(
                    camtoworld=pose,
                    K=K,
                    width=config.width,
                    height=config.height,
                )
                
                # Convert to numpy and clamp
                frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
                frame = (frame * 255).astype(np.uint8)
                frames.append(frame)
                
                # Save individual frame
                imageio.imwrite(
                    os.path.join(config.result_dir, f"frame_{i:04d}.png"),
                    frame
                )
        
        # Save video if requested
        if config.save_video:
            video_path = os.path.join(config.result_dir, f"{config.render_mode}.mp4")
            imageio.mimsave(video_path, frames, fps=config.fps)
            print(f"Video saved to: {video_path}")
        
        print(f"Rendered {len(frames)} frames to: {config.result_dir}")
    
    def render_single_view(self, pose: torch.Tensor, K: torch.Tensor, output_path: str):
        """Render a single view."""
        with torch.no_grad():
            rendered = self.rasterize_splats(
                camtoworld=pose,
                K=K,
                width=self.config.width,
                height=self.config.height,
            )
            
            frame = torch.clamp(rendered, 0.0, 1.0).cpu().numpy()
            frame = (frame * 255).astype(np.uint8)
            
            imageio.imwrite(output_path, frame)
            print(f"Rendered view saved to: {output_path}")


def main():
    """Main rendering function."""
    config = tyro.cli(RenderConfig)
    
    print("Starting keyframes 3DGS rendering...")
    print(f"Config: {config}")
    
    renderer = KeyframesRenderer(config)
    renderer.render_trajectory()
    
    print("Rendering completed!")


if __name__ == "__main__":
    main()
